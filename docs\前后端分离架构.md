# 前后端分离架构 - 综合技术规范与设计文档 (V4.0 Production-Ready)

## 第1部分：架构与顶层设计

### 1.1 核心设计哲学：权责分离
- **后端 (Brain)**: 业务逻辑、数据持久化、身份认证、权限决策的**唯一可信来源(Single Source of Truth)**。
- **前端 (Eyes & Hands)**: 纯粹的UI渲染与交互执行层，严格根据后端数据动态构建界面，**不包含任何硬编码的业务或权限逻辑**。

### 1.2 技术栈概览
- **前端**: `React 18`, `TypeScript`, `Ant Design`, `Vite`, `React Router`, `Axios`, `TanStack Query (React Query)`, `Zustand` (可选).
- **后端**: `FastAPI`, `Python`, `PostgreSQL`, `SQLAlchemy`, `Uvicorn`, `Pydantic`, `JWT`.

## 第2部分：代码规范与命名约定

### 2.1 Git 规范
- **分支模型**: 采用 `Git Flow` (master, develop, feature/xxx, release/xxx, hotfix/xxx)。
- **提交信息**: 必须遵循**[约定式提交 (Conventional Commits)](https://www.conventionalcommits.org/)**规范。
  - `feat`: 新功能
  - `fix`: Bug修复
  - `docs`: 文档变更
  - `style`: 代码风格 (不影响代码运行的变动)
  - `refactor`: 重构
  - `test`: 增加测试
  - `chore`: 构建过程或辅助工具的变动
  - **示例**: `feat(user): add user creation endpoint`

### 2.2 前端命名与风格规范
- **组件**: 大驼峰命名法 (PascalCase)，文件名与组件名一致，如 `UserInfoCard.tsx`。
- **变量/函数**: 小驼峰命名法 (camelCase)，如 `const currentUser`。
- **Hooks**: 以 `use` 开头，如 `useUserData.ts`。
- **API 调用函数**: `verb + Noun(s)` 格式，如 `getUsers()`, `updateRole(id, data)`。
- **CSS**: 推荐使用 `CSS-in-JS` 或 `CSS Modules` 避免全局污染。若使用全局CSS，需遵循 `BEM` 命名约定。
- **代码风格**: 项目根目录提供 `.eslintrc.js` 和 `.prettierrc` 配置文件，并配置 `husky` 和 `lint-staged` 在代码提交前自动格式化和检查。

### 2.3 后端命名与风格规范
- **文件名**: 蛇形命名法 (snake_case)，如 `user_router.py`。
- **变量/函数**: 蛇形命名法 (snake_case)，如 `current_user`。
- **类名**: 大驼峰命名法 (PascalCase)，如 `class User(Base):`。
- **代码风格**: 遵循 `PEP 8` 规范，使用 `black` 和 `isort` 进行自动格式化，配置 `flake8` 或 `ruff` 进行代码检查。

## 第3部分：API 设计与交互契约

### 3.1 核心契约：OpenAPI 规范
- **唯一可信源**: 项目将维护一份 `OpenAPI 3.0` 规范文件 (如 `openapi.yaml`)，由**后端负责定义和更新**。
- **作用**:
  - **后端**: 可基于此文件自动生成交互式API文档 (Swagger UI / ReDoc)。
  - **前端**: 可基于此文件自动生成 TypeScript 的类型定义和 API 客户端代码，确保与后端完全同步。

### 3.2 通用接口规范
- **根路径**: `/api`
- **认证**: `JWT`, 在 Header 中传递 `Authorization: Bearer <token>`。
- **数据格式**: `JSON (application/json)`。
- **CORS**: 后端配置 `CORSMiddleware`，允许前端开发服务器地址跨域访问。

### 3.3 统一响应体结构
所有API端点必须返回此结构：
```typescript
interface ApiResponse<T = any> {
  code: number;      // 业务状态码 (0: 成功, 非0: 失败)
  message: string;   // 响应消息
  data: T | null;    // 核心业务数据
}
```

### 3.4 列表接口规范 (分页、排序、过滤)
所有返回列表数据的 `GET` 请求，必须支持以下查询参数，并返回包含分页信息的统一结构。

- **请求查询参数**:
  - `page: number` (页码, 从1开始, 默认1)
  - `size: number` (每页大小, 默认20)
  - `sort_by: string` (排序字段, 如 `created_at`)
  - `order: 'asc' | 'desc'` (排序顺序, 默认 `desc`)
  - 其他业务相关的过滤参数, 如 `is_active: boolean`。

- **成功响应 (`data` 字段) 结构**:
  ```typescript
  interface PaginatedData<T> {
    items: T[];       // 当前页的数据列表
    total: number;    // 总记录数
    page: number;     // 当前页码
    size: number;     // 每页大小
  }
  ```
- **示例**: `GET /api/users?page=2&size=10&sort_by=username&order=asc`

### 3.5 详细的数据模型与校验规则
所有数据模型及其字段约束，都必须在 `OpenAPI` 规范文件中进行详细定义。
- **示例 (`User` 模型的一个字段)**:
  ```yaml
  # في openapi.yaml
  components:
    schemas:
      UserCreate:
        type: object
        required:
          - username
          - email
          - password
        properties:
          username:
            type: string
            description: "用户名"
            minLength: 3
            maxLength: 50
          email:
            type: string
            format: email
            description: "用户邮箱"
          # ... 其他字段
  ```

## 第4部分：前端架构与实现策略

### 4.1 状态管理策略
- **全局业务无关状态**: 使用 `Zustand` (轻量级) 或 `Redux Toolkit` (重量级) 管理。
  - **管理内容**: 用户信息 (`user`)、权限 (`permissions`)、应用主题、全局配置等。
- **服务端缓存状态**: **必须使用 `TanStack Query (React Query)`** 来管理所有与服务端API交互的状态。
  - **职责**: 自动处理数据的获取、缓存、后台静默更新、加载状态 (`isLoading`)、错误状态 (`isError`) 和分页。
  - **禁止行为**: **严禁**使用 `useState` + `useEffect` 的方式来手动获取和管理服务端数据。
- **组件本地状态**: 仅用于管理与UI交互直接相关的、无需跨组件共享的状态（如表单输入、弹窗的开关等），使用 `useState` 或 `useReducer`。

### 4.2 统一请求与响应封装
在 `src/services/api.ts` 中实现，包含一个深度配置的 `Axios` 实例。
- **请求拦截器**: 统一注入 `Authorization` Header。
- **响应拦截器**:
  1. **成功处理**: 检查业务码 `code`，若为 `0`，则直接将 `data` 部分返回给 `React Query`；若非 `0`，显示全局错误消息并向上抛出错误。
  2. **异常处理**: 根据 `HTTP状态码` (401, 403, 500等) 进行集中的、分类的全局错误消息提示。对于 `401`，必须触发全局登出逻辑。

## 第5部分：后端架构与实现策略

### 5.1 统一响应与异常处理
- **实现方式**: 通过 FastAPI 的全局异常处理器 (`@app.exception_handler`) 实现。
- **处理器职责**:
  1. **捕获自定义业务异常** (`ApiException`)，并格式化为统一响应体。
  2. **捕获HTTP异常** (`HTTPException`)，并格式化。
  3. **捕获Pydantic校验异常** (`RequestValidationError`)，并格式化。
  4. **捕获所有未知异常** (`Exception`)，记录详细日志，并返回统一的 `500` 错误响应。
- **API代码要求**: 业务逻辑中**只应**抛出 `ApiException` 或调用 `success()` 返回成功响应，不应直接返回 `JSONResponse` 或其他响应类型。

---

## 第6部分：核心交互流程 (落地指南)

此流程描述了上述所有规范如何协同工作。

### 阶段一：身份验证
1. **前端**: 用户提交表单，调用 `login(username, password)` API 函数。
2. **后端**: `POST /api/auth/token` 接口验证凭证，成功后返回包含 `JWT Token` 和 `User` 信息的统一成功响应。
3. **前端**:
   - `Axios` 响应拦截器解析响应，确认 `code === 0`。
   - `login` 函数的 `.then()` 回调中拿到 `data`。
   - 将 `Token` 存入 `LocalStorage`。
   - 将 `User` 信息存入全局状态管理 (`Zustand`)。
   - 调用 `React Query` 的 `queryClient.invalidateQueries()` 来使所有依赖用户权限的数据失效，触发自动重新获取。

### 阶段二：动态UI构建
1. **前端**: 页面加载或登录成功后，一个专门的 `usePermissions` Hook 被调用。
2. **`usePermissions` Hook**: 内部使用 `React Query` 的 `useQuery` 来调用 `GET /api/menus/me`。`React Query` 会自动处理加载状态和缓存。
3. **后端**: `/api/menus/me` 接口验证权限，返回包含用户专属菜单树和操作权限的统一成功响应。
4. **前端**:
   - `useQuery` 成功后拿到 `data` (菜单树)。
   - 使用 `useMemo` 计算并生成“权限地图”(PermissionMap)，供全应用消费。
   - 导航菜单组件根据菜单树数据进行动态渲染。

### 阶段三：用户操作 (以删除用户为例)
1. **前端**: 用户在列表中点击删除按钮。该按钮通过 `PermissionButton` 组件渲染，其 `disabled` 状态已根据“权限地图”预先判断。
2. **前端**: 调用 `useMutation` (来自 `React Query`) 来执行删除操作。
   ```javascript
   const deleteUserMutation = useMutation({
     mutationFn: (userId) => api.delete(`/users/${userId}`),
     onSuccess: () => {
       AntdMessage.success('删除成功');
       queryClient.invalidateQueries({ queryKey: ['users'] }); // 使"用户列表"缓存失效，React Query会自动重新获取最新列表
     },
     // onError 已由 Axios 拦截器统一处理，这里可留空
   });
   
   const handleDelete = (id) => deleteUserMutation.mutate(id);
   ```
3. **后端**: `DELETE /api/users/{id}` 接口进行最终的权限校验。
   - 校验失败，抛出 `ApiException(message="无权删除", code=4030)`，全局处理器返回统一错误响应。
   - 校验成功，执行删除，返回 `204 No Content` 或包含成功信息的统一响应。
4. **前端**:
   - `Axios` 响应拦截器处理响应。
   - `useMutation` 的 `onSuccess` 回调被触发，显示成功消息并刷新用户列表。