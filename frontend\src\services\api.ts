import axios, { type AxiosError } from 'axios';
import { message } from 'antd';
import useAuthStore from '../store/authStore';
import type { LoginCredentials, AuthResponse, UserCreate, UserUpdate, RoleCreate, RoleUpdate, RolePermissionAssignment, MenuCreate, MenuUpdate } from './types';
import type { Menu, User, Role, Permission } from '../types/entities';

// Create an Axios instance
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add the auth token to headers
apiClient.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => {
    console.error('Request Error:', error);
    message.error(error.message || 'Request failed, please check your network.');
    return Promise.reject(error);
  }
);

// Response interceptor to handle global errors
apiClient.interceptors.response.use(
  (response) => response,
  (error: AxiosError<{ detail?: string | [{ msg: string }] }>) => {
    console.error('Response Error:', error.response);

    if (error.response?.status === 401) {
      message.error('Authentication failed, please log in again.');
      // By calling logout, we clear the token and user info,
      // the ProtectedRoute will automatically redirect to /login
      useAuthStore.getState().logout();
    } else if (error.response) {
      const errorMsg = error.response.data?.detail;
      if (typeof errorMsg === 'string') {
        message.error(errorMsg);
      } else if (Array.isArray(errorMsg)) {
        errorMsg.forEach(err => message.error(err.msg));
      } else {
        message.error(`Error: ${error.response.status} ${error.response.statusText}`);
      }
    } else if (error.request) {
      message.error('No response received from the server. Please check your connection.');
    } else {
      message.error('An unexpected error occurred.');
    }

    return Promise.reject(error);
  }
);

// --- Auth ---
export const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  // Note: for login, the content type is application/x-www-form-urlencoded
  const form = new URLSearchParams();
  form.append('username', credentials.username || '');
  form.append('password', credentials.password || '');
  const { data } = await apiClient.post<AuthResponse>('/api/auth/token', form, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
  return data;
};

// --- Menu ---
export const getMyMenus = async (): Promise<Menu[]> => {
  const { data } = await apiClient.get<Menu[]>('/api/menus/me');
  return data;
};

// --- User Management ---
export const getUsers = async (): Promise<User[]> => {
  const { data } = await apiClient.get<User[]>('/api/users/');
  return data;
};

export const createUser = async (userData: UserCreate): Promise<User> => {
  const { data } = await apiClient.post<User>('/api/users/', userData);
  return data;
};

export const updateUser = async (userId: number, userData: UserUpdate): Promise<User> => {
  const { data } = await apiClient.put<User>(`/api/users/${userId}`, userData);
  return data;
};

export const deleteUser = async (userId: number): Promise<User> => {
  const { data } = await apiClient.delete<User>(`/api/users/${userId}`);
  return data;
};

// --- Role Management ---
export const getRoles = async (): Promise<Role[]> => {
  const { data } = await apiClient.get<Role[]>('/api/roles/');
  return data;
};

export const createRole = async (roleData: RoleCreate): Promise<Role> => {
  const { data } = await apiClient.post<Role>('/api/roles/', roleData);
  return data;
};

export const updateRole = async (roleId: number, roleData: RoleUpdate): Promise<Role> => {
  const { data } = await apiClient.put<Role>(`/api/roles/${roleId}`, roleData);
  return data;
};

export const deleteRole = async (roleId: number): Promise<Role> => {
  const { data } = await apiClient.delete<Role>(`/api/roles/${roleId}`);
  return data;
};

export const assignPermissionsToRole = async (roleId: number, permissionIds: number[]): Promise<Role> => {
  const payload: RolePermissionAssignment = { permission_ids: permissionIds };
  const { data } = await apiClient.post<Role>(`/api/roles/${roleId}/permissions`, payload);
  return data;
};

// --- Permission Management ---
export const getPermissions = async (): Promise<Permission[]> => {
  const { data } = await apiClient.get<Permission[]>('/api/permissions/');
  return data;
};

// --- Menu Management (Admin) ---
export const getMenus = async (): Promise<Menu[]> => {
  const { data } = await apiClient.get<Menu[]>('/api/menus/');
  return data;
};

export const createMenu = async (menuData: MenuCreate): Promise<Menu> => {
  const { data } = await apiClient.post<Menu>('/api/menus/', menuData);
  return data;
};

export const updateMenu = async (menuId: number, menuData: MenuUpdate): Promise<Menu> => {
  const { data } = await apiClient.put<Menu>(`/api/menus/${menuId}`, menuData);
  return data;
};

export const deleteMenu = async (menuId: number): Promise<Menu> => {
  const { data } = await apiClient.delete<Menu>(`/api/menus/${menuId}`);
  return data;
};

export default apiClient; 