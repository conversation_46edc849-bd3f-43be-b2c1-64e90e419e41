# 企业级前后端分离应用基础框架

本文档是项目的主要入口，为开发人员提供了全面的指南，涵盖了从项目理解、环境搭建到开发规范的各个方面。

## 1. 项目简介

本项目是一个现代化的、前后端分离的企业级 Web 应用基础框架。它旨在提供一个稳定、安全、可扩展的起点，其核心是一套完整的用户认证与 **基于角色权限的访问控制 (RBAC)** 系统，并配套提供了相应的管理界面，使开发团队能够在此基础上快速构建复杂的业务功能模块。

**核心目标:**
- **提供坚实的安全基础**: 内置强大且灵活的 RBAC 权限系统。
- **加速业务开发**: 提供标准化的开发模式、基础组件和管理后台。
- **保证技术架构的先进性**: 采用业界主流且成熟的前后端分离架构。
- **提供高质量的用户体验**: 基于现代前端框架和高质量的组件库。

> 详细信息请参阅: [`docs/项目介绍文档.md`](docs/项目介绍文档.md)

## 2. 技术栈

|              | **前端 (Frontend)**                                                                                             | **后端 (Backend)**                                                                                                  |
|--------------|-----------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------|
| **核心框架**     | `React 18`                                                                                                      | `FastAPI`                                                                                                         |
| **编程语言**   | `TypeScript`                                                                                                    | `Python`                                                                                                          |
| **UI 组件库**    | `Ant Design (AntD)`                                                                                             | -                                                                                                                 |
| **状态管理** | `TanStack Query (React Query)` (服务端状态), `Zustand` (全局状态) | - |
| **路由管理**     | `React Router`                                                                                                  | -                                                                                                                 |
| **HTTP 通信**  | `Axios`                                                                                                         | -                                                                                                                 |
| **构建工具**     | `Vite`                                                                                                          | -                                                                                                                 |
| **Web 服务器** | -                                                                                                               | `Uvicorn`                                                                                                         |
| **数据库**       | -                                                                                                               | `PostgreSQL`                                                                                                      |
| **ORM**        | -                                                                                                               | `SQLAlchemy`                                                                                                      |
| **数据校验**     | -                                                                                                               | `Pydantic`                                                                                                        |
| **认证机制**     | -                                                                                                               | `JWT (JSON Web Tokens)`                                                                                           |
| **代码规范**     | `ESLint`, `Prettier`                                                                                            | `black`, `isort`, `flake8`                                                                                        |

> 详细技术栈说明请参阅: [`docs/前后端分离架构.md`](docs/前后端分离架构.md)

## 3. 架构概览

项目采用经典的前后端分离架构，职责清晰：

- **后端 (Brain)**: 位于 `/backend` 目录。作为**唯一可信来源(Single Source of Truth)**，负责业务逻辑、数据持久化、身份认证和权限决策。
- **前端 (Eyes & Hands)**: 位于 `/frontend` 目录。作为纯粹的UI渲染与交互执行层，严格根据后端数据动态构建界面，**不包含任何硬编码的业务或权限逻辑**。

两者之间通过基于 **JWT** 认证的 **RESTful API** 进行通信。

## 4. 核心设计

### 4.1 RBAC 权限模型
系统的核心是 "用户-角色-权限" 的 RBAC 模型。用户通过扮演角色来获得操作权限，从而实现授权管理的简化和最小权限原则。

### 4.2 权限与菜单联动生成
为了简化管理，后端实现了一套自动化流程：当一个**菜单**被创建或其路径被更新时，系统会**自动地为其创建或更新一套关联的基础操作权限** (如: `view`, `create`, `edit`, `delete`)。这确保了权限与功能菜单的实时同步。

> RBAC 与核心功能设计的详细信息请参阅: [`docs/项目RBAC与核心功能设计文档.md`](docs/项目RBAC与核心功能设计文档.md)

## 5. 开发入门指南

### 环境准备
- `Node.js` (推荐 v16+ an v18+)
- `Python` (推荐 3.8+)
- `PostgreSQL` 数据库

### 后端设置 (`/backend`)
1. **进入目录**: `cd backend`
2. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```
3. **配置环境变量**: 在 `backend` 目录下创建 `.env` 文件。此文件用于存储数据库连接等敏感信息。
   ```dotenv
   # backend/.env
   DATABASE_URL="postgresql://postgres:postgres@localhost:5432/webbasedb2?client_encoding=utf8"
   ```
4. **启动服务**:
   ```bash
   python run.py
   ```
   服务启动时会自动根据 `backend/models.py` 创建数据库表，并执行 `backend/init_db.py` 填充初始数据 (管理员账户、角色、权限等)。
5. **访问 API 文档**: 服务运行后，可访问 [http://localhost:8000/docs](http://localhost:8000/docs) 查看并交互式调试所有 API。

> 详细配置说明请参阅: [`docs/后端核心配置与启动说明.md`](docs/后端核心配置与启动说明.md)

### 前端设置 (`/frontend`)
1. **进入目录**: `cd frontend`
2. **安装依赖**:
   ```bash
   npm install
   ```
3. **配置环境变量**: 在 `frontend` 目录下创建 `.env.development` 文件，指向本地后端服务地址。
   ```dotenv
   # frontend/.env.development
   VITE_API_BASE_URL="http://localhost:8000"
   ```
4. **启动服务**:
   ```bash
   npm run dev
   ```
5. **访问应用**: 在浏览器中打开 `http://localhost:5173` (或 Vite 启动时提示的地址)。

## 6. 代码规范

项目对 Git 工作流、代码提交信息、以及前后端代码风格和命名都做了详细约定。配置了 `husky`, `lint-staged`, `prettier`, `eslint`, `black` 等工具来保障代码质量。

**请所有开发者在开始编码前，务必详细阅读代码规范文档。**

> 规范详情请参阅: [`docs/前后端分离架构.md`](docs/前后端分离架构.md) (第2部分)

## 7. 重要文档索引

- [`docs/项目介绍文档.md`](docs/项目介绍文档.md): 了解项目目标、技术选型和整体架构。
- [`docs/前后端分离架构.md`](docs/前后端分离架构.md): 深入理解核心设计哲学、代码规范和API交互契约。
- [`docs/项目RBAC与核心功能设计文档.md`](docs/项目RBAC与核心功能设计文档.md): 掌握权限系统的设计、数据模型和核心流程。
- [`docs/前端主布局UI或UX 设计需求文档.md`](docs/前端主布局UI或UX 设计需求文档.md): 查看前端主布局的设计规约。
- [`docs/后端核心配置与启动说明.md`](docs/后端核心配置与启动说明.md): 获取后端配置和启动的详细指南。
- [`docs/项目API文档.md`](docs/项目API文档.md): 查阅所有后端API的详细信息 (推荐直接访问 [http://localhost:8000/docs](http://localhost:8000/docs) 查看实时文档)。 