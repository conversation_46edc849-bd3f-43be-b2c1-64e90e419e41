#!/usr/bin/env python3
"""
清空PostgreSQL数据库的脚本
"""
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/.env')

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/webbasedb2")

def clear_database():
    """清空数据库中的所有表"""
    try:
        print(f"数据库URL: {DATABASE_URL}")

        # 直接使用psycopg2连接字符串
        conn = psycopg2.connect(DATABASE_URL)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
            
        print("正在获取所有表名...")

        # 获取所有用户表
        cursor.execute("""
            SELECT tablename FROM pg_tables
            WHERE schemaname = 'public'
        """)
        tables = cursor.fetchall()

        if not tables:
            print("数据库中没有找到表")
            return

        print(f"找到 {len(tables)} 个表")

        # 禁用外键约束检查并删除所有表
        print("正在删除所有表...")
        for table in tables:
            table_name = table[0]
            print(f"删除表: {table_name}")
            cursor.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE")

        # 删除所有序列
        print("正在删除所有序列...")
        cursor.execute("""
            SELECT sequence_name FROM information_schema.sequences
            WHERE sequence_schema = 'public'
        """)
        sequences = cursor.fetchall()

        for sequence in sequences:
            sequence_name = sequence[0]
            print(f"删除序列: {sequence_name}")
            cursor.execute(f"DROP SEQUENCE IF EXISTS {sequence_name} CASCADE")

        # 重新创建public schema
        print("正在重新创建public schema...")
        cursor.execute("CREATE SCHEMA IF NOT EXISTS public")
        cursor.execute("GRANT ALL ON SCHEMA public TO postgres")
        cursor.execute("GRANT ALL ON SCHEMA public TO public")

        cursor.close()
        conn.close()

        print("✅ 数据库清空完成！")
            
    except Exception as e:
        print(f"❌ 清空数据库时出错: {e}")
        raise

if __name__ == "__main__":
    print("🗑️  开始清空PostgreSQL数据库...")
    clear_database()
