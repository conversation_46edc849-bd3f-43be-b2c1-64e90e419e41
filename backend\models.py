from sqlalchemy import <PERSON>um<PERSON>, <PERSON>te<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Table
from sqlalchemy.orm import relationship
from .database import Base

# User-Role Association Table for Many-to-Many
user_roles = Table('user_roles', Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id', ondelete="CASCADE"), primary_key=True),
    Column('role_id', Integer, Foreign<PERSON>ey('roles.id', ondelete="CASCADE"), primary_key=True)
)

# Role-Permission Association Table for Many-to-Many
role_permissions = Table('role_permissions', Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id', ondelete="CASCADE"), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id', ondelete="CASCADE"), primary_key=True)
)

class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)

    roles = relationship("Role", secondary=user_roles, back_populates="users")

class Role(Base):
    __tablename__ = 'roles'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")

class Permission(Base):
    __tablename__ = 'permissions'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(255), nullable=False)
    menu_id = Column(Integer, ForeignKey('menus.id', ondelete="SET NULL"), nullable=True)

    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")
    menu = relationship("Menu", back_populates="permissions")

class Menu(Base):
    __tablename__ = 'menus'
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    path = Column(String(255), unique=True, nullable=False)
    parent_id = Column(Integer, ForeignKey('menus.id', ondelete="CASCADE"), nullable=True)
    order = Column(Integer, default=0, nullable=False)
    icon = Column(String(50), nullable=True)

    permissions = relationship("Permission", back_populates="menu")
    parent = relationship("Menu", remote_side=[id], back_populates="children")
    children = relationship("Menu", back_populates="parent") 