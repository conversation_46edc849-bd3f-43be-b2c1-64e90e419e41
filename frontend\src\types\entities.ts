export interface Permission {
  id: number;
  name: string;
  display_name: string;
  menu_id?: number;
}

export interface Role {
  id: number;
  name: string;
  display_name: string;
  permissions: Permission[];
}

export interface User {
  id: number;
  username: string;
  is_active: boolean;
  roles: Role[];
}

export interface Menu {
  id: number;
  key: string;
  display_name: string;
  path: string;
  parent_id: number | null;
  order: number;
  icon?: string;
  children?: Menu[]; // 用于前端构建树形结构
} 