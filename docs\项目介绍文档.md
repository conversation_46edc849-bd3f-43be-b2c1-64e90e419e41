# 项目介绍文档 (Project Overview)

**版本:** 1.0

### 1. 项目简介

本项目是一个现代化的、前后端分离的企业级 Web 应用基础框架。它旨在提供一个稳定、安全、可扩展的起点，使开发团队能够在此基础上快速构建复杂的业务功能模块。

该框架的核心是一套完整的用户认证与 **基于角色权限的访问控制 (RBAC)** 系统，并配套提供了相应的管理界面。

### 2. 核心目标

-   **提供坚实的安全基础**: 内置强大且灵活的 RBAC 权限系统，能够精细化地控制每个用户对不同功能模块的访问和操作权限。
-   **加速业务开发**: 提供一套标准化的开发模式、基础组件和管理后台，让开发者可以专注于业务逻辑的实现，而非重复性的基础架构工作。
-   **保证技术架构的先进性与可维护性**: 采用业界主流且成熟的前后端分离架构，确保前后端职责清晰，可以独立开发、测试、部署和扩展。
-   **提供高质量的用户体验**: 基于现代前端框架和高质量的组件库，确保最终用户能够获得流畅、一致且美观的界面体验。

### 3. 技术栈
#### **前端技术栈 (Frontend)**

前端是一个基于 **React** 的现代化单页应用 (SPA)。

-   **核心框架 (Core Framework):** `React 18`
    -   *描述*：用于构建用户界面的核心 JavaScript 库。
-   **编程语言 (Programming Language):** `TypeScript`
    -   *描述*：为 JavaScript 添加了静态类型，提高了代码的可维护性和健壮性。
-   **UI 组件库 (UI Component Library):** `Ant Design (AntD)`
    -   *描述*：一套企业级的 UI 设计语言和 React 实现，提供了丰富的、开箱即用的高质量组件。
-   **路由管理 (Routing):** `React Router`
    -   *描述*：用于处理应用内的页面跳转和路由逻辑。
-   **HTTP 通信 (HTTP Communication):** `Axios`
    -   *描述*：一个流行的、基于 Promise 的 HTTP客户端，用于与后端 API 进行数据交互。
-   **构建与开发工具 (Build & Dev Tool):** `Vite`
    -   *描述*：一个现代化的前端构建工具，提供了极速的冷启动和热模块替换 (HMR)。
-   **代码规范 (Linting):** `ESLint`
    -   *描述*：用于检查代码风格和潜在错误，保证代码质量。

---

#### **后端技术栈 (Backend)**

后端是一个基于 **FastAPI** 的高性能 Python 服务。

-   **核心框架 (Core Framework):** `FastAPI`
    -   *描述*：一个现代、高性能的 Python Web 框架，用于构建 API，并能自动生成交互式文档。
-   **编程语言 (Programming Language):** `Python`
-   **Web 服务器 (Web Server):** `Uvicorn`
    -   *描述*：一个轻量级、速度极快的 ASGI (Asynchronous Server Gateway Interface) 服务器。
-   **数据库 (Database):** `PostgreSQL`
    -   *描述*：通过依赖 `psycopg2-binary` (PostgreSQL 的 Python 驱动) 推断得出，是一个功能强大的开源对象关系型数据库。
-   **对象关系映射 (ORM):** `SQLAlchemy`
    -   *描述*：一个强大的 Python SQL 工具包和 ORM，用于以面向对象的方式操作数据库。
-   **数据校验与模型 (Data Validation & Models):** `Pydantic`
    -   *描述*：用于数据校验、序列化和设置管理的核心库。
-   **认证机制 (Authentication):** `JWT (JSON Web Tokens)`
    -   *描述*：通过 `python-jose` 库实现，用于生成和验证无状态的认证 token。
-   **密码处理 (Password Handling):** `Passlib` (with `bcrypt`)
    -   *描述*：一个强大的密码哈希库，用于安全地存储和验证用户密码。
-   **配置文件处理 (Config Handling):** `python-dotenv`
    -   *描述*：用于从 `.env` 文件中读取环境变量，实现配置与代码的分离。

---

## **架构总结**

这是一个经典的**前后端分离**架构：
*   **前端**使用 `Vite` + `React` + `TypeScript` 构建，负责用户界面和交互。
*   **后端**使用 `Python` + `FastAPI` + `PostgreSQL` 构建，负责提供 RESTful API 和处理业务逻辑。
*   两者之间通过 **Axios** 发起基于 **JWT** 认证的 HTTP 请求进行通信。

### 4. 项目架构

本项目采用经典的前后端分离架构模式。

-   **后端 (`/backend` 目录)**
    -   一个基于 **FastAPI** 的独立应用。
    -   **核心职责**:
        1.  提供所有业务逻辑的 **RESTful API** 接口。
        2.  处理所有与 **PostgreSQL** 数据库的交互（通过 SQLAlchemy）。
        3.  管理用户认证、会话和权限校验。
        4.  不负责任何界面的渲染。

-   **前端 (`/frontend` 目录)**
    -   一个基于 **React** 和 **Vite** 的单页面应用 (SPA)。
    -   **核心职责**:
        1.  构建用户看到的所有界面和交互。
        2.  通过调用后端 API 来获取和提交数据。
        3.  管理应用的客户端状态。
        4.  使用 **Ant Design** 作为核心 UI 组件库，保证界面的一致性和美观性。

### 5. 核心功能模块

1.  **统一认证与动态路由**
    -   用户通过统一登录页面进行登录，后端使用 JWT 进行认证。
    -   前端会根据登录用户所拥有的权限，动态生成导航菜单和注册页面路由，确保用户只能访问其被授权的页面。

2.  **精细化的权限控制 (RBAC)**
    -   **用户管理**: 创建、编辑、查询和管理系统中的所有用户，并为用户分配角色。
    -   **角色管理**: 创建和定义不同的角色（如管理员、操作员、访客）。
    -   **权限分配**: 以直观的树形控件，为每个角色精确分配它可以访问的**菜单权限**和在页面上可以执行的**操作权限**（如增、删、改、查）。

3.  **系统管理**
    -   **菜单管理**: 允许管理员通过界面配置系统的导航菜单结构，菜单的变动会自动关联到权限系统中。

4.  **业务模块容器**
    -   框架本身是一个强大的容器，已预留了清晰的结构用于添加新的业务模块，例如“仪表盘”、“客户管理”、“报表系统”等。

### 6. 如何开始

-   **初次上手**: 请首先阅读 **`后端核心配置与启动说明.md`** 和 **`前端开发环境搭建指南.md`**，以在您本地成功运行本项目。
-   **API 对接**: 在进行前端开发时，请参考 **`API文档.md`** 或在后端服务启动后访问 `http://localhost:8000/docs` 获取可交互的实时 API 文档。
-   **深入理解**: 若要深入理解项目的核心设计理念，请查阅 **`项目 RBAC 与核心功能设计文档`** 和 **`前后端分离架构.txt`**。
