from sqlalchemy.orm import Session
from .. import models, schemas
from . import permission_service

BASE_PERMISSIONS = ["view", "create", "edit", "delete"]

def _generate_permission_name(menu_key: str, action: str):
    """
    生成权限名称，例如：'user_management:view'
    """
    return f"{menu_key}:{action}"

def _sync_permissions_for_menu(db: Session, menu: models.Menu):
    """
    为一个菜单同步创建或删除其基础权限。
    这是此服务的核心逻辑。
    """
    existing_permissions = {p.name for p in permission_service.get_permissions_by_menu_id(db, menu.id)}
    
    # 1. 创建缺失的权限
    for action in BASE_PERMISSIONS:
        permission_name = _generate_permission_name(menu.key, action)
        if permission_name not in existing_permissions:
            permission_display_name = f"{menu.display_name} - {action.capitalize()}"
            db_permission = models.Permission(
                name=permission_name,
                display_name=permission_display_name,
                menu_id=menu.id
            )
            db.add(db_permission)

    # 2. （可选）删除不再匹配的权限，如果 key 改变了
    # 在这个实现中，我们假设 key 改变时，旧的权限需要手动清理或保留
    # 为简化，我们只处理创建
    db.flush()


def get_menu(db: Session, menu_id: int):
    return db.query(models.Menu).filter(models.Menu.id == menu_id).first()

def get_menus(db: Session):
    return db.query(models.Menu).order_by(models.Menu.order).all()

def create_menu(db: Session, menu: schemas.MenuCreate):
    db_menu = models.Menu(**menu.model_dump())
    db.add(db_menu)
    db.flush() # 使用 flush 来获取 db_menu 的 ID，但不结束事务
    
    # 核心逻辑：创建关联权限
    _sync_permissions_for_menu(db, db_menu)
    
    db.commit() # 在所有操作完成后，只 commit 一次
    db.refresh(db_menu)
    
    return db_menu

def update_menu(db: Session, menu_id: int, menu_update: schemas.MenuUpdate):
    db_menu = get_menu(db, menu_id)
    if db_menu:
        update_data = menu_update.model_dump(exclude_unset=True)
        
        # 记录 key 是否改变
        key_changed = 'key' in update_data and update_data['key'] != db_menu.key
        
        for key, value in update_data.items():
            setattr(db_menu, key, value)
        
        db.commit()
        db.refresh(db_menu)
        
        # 核心逻辑：如果 key 改变了，则同步权限
        # 注意：这里简化为只创建新的，不删除旧的。
        # 一个更完整的实现可能需要处理旧权限的迁移或删除。
        if key_changed:
            _sync_permissions_for_menu(db, db_menu)
            db.commit()
            db.refresh(db_menu)
            
    return db_menu

def delete_menu(db: Session, menu_id: int):
    db_menu = get_menu(db, menu_id)
    if db_menu:
        # 核心逻辑：删除菜单前，先删除其所有关联权限
        associated_permissions = permission_service.get_permissions_by_menu_id(db, menu_id)
        for perm in associated_permissions:
            db.delete(perm)
        
        db.delete(db_menu)
        db.commit()
    return db_menu 