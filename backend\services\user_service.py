from sqlalchemy.orm import Session, joinedload
from .. import models, schemas, security
from typing import List, Optional

def get_user_by_username(db: Session, username: str) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.username == username).first()

def authenticate_user(db: Session, username: str, password: str) -> Optional[models.User]:
    user = get_user_by_username(db, username)
    if not user:
        return None
    if not security.verify_password(password, user.hashed_password):
        return None
    return user

def get_user(db: Session, user_id: int):
    """
    通过ID获取用户
    """
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_email(db: Session, email: str):
    return db.query(models.User).options(
        joinedload(models.User.roles).joinedload(models.Role.permissions)
    ).filter(models.User.email == email).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[models.User]:
    """
    获取用户列表（分页）
    """
    return db.query(models.User).offset(skip).limit(limit).all()

def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    """
    创建新用户
    """
    hashed_password = security.get_password_hash(user.password)
    # Create user object without role_ids, as it's not a model field
    db_user = models.User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Now, handle the role assignment
    if user.role_ids:
        roles = db.query(models.Role).filter(models.Role.id.in_(user.role_ids)).all()
        db_user.roles.extend(roles)
        db.commit()
        db.refresh(db_user)

    return db_user

def update_user(db: Session, user_id: int, user_update: schemas.UserUpdate):
    """
    更新用户信息
    """
    db_user = get_user(db, user_id)
    if not db_user:
        return None

    update_data = user_update.model_dump(exclude_unset=True)

    if "password" in update_data and update_data["password"]:
        hashed_password = security.get_password_hash(update_data["password"])
        db_user.hashed_password = hashed_password
        del update_data["password"]

    if "role_ids" in update_data:
        role_ids = update_data.pop("role_ids")
        if role_ids is not None:
            roles = db.query(models.Role).filter(models.Role.id.in_(role_ids)).all()
            db_user.roles = roles

    for key, value in update_data.items():
        setattr(db_user, key, value)

    db.commit()
    db.refresh(db_user)
    return db_user

def delete_user(db: Session, user_id: int):
    """
    删除用户
    """
    db_user = get_user(db, user_id)
    if db_user:
        db.delete(db_user)
        db.commit()
    return db_user 