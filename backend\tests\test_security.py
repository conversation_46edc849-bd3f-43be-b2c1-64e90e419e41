import pytest
from jose import jwt, J<PERSON><PERSON><PERSON>r
from datetime import timedelta
from backend import security

# Test data
TEST_PASSWORD = "testpassword123"
TEST_USERNAME = "testuser"
ACCESS_TOKEN_EXPIRE_MINUTES = security.ACCESS_TOKEN_EXPIRE_MINUTES
SECRET_KEY = security.SECRET_KEY
ALGORITHM = security.ALG<PERSON>ITH<PERSON>

def test_password_hashing():
    """
    Tests that password hashing and verification work correctly.
    """
    hashed_password = security.get_password_hash(TEST_PASSWORD)
    assert hashed_password != TEST_PASSWORD
    assert security.verify_password(TEST_PASSWORD, hashed_password)
    assert not security.verify_password("wrongpassword", hashed_password)

def test_create_access_token():
    """
    Tests the creation of a JWT access token.
    """
    token = security.create_access_token(data={"sub": TEST_USERNAME})
    assert isinstance(token, str)

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALG<PERSON><PERSON>HM])
        assert payload.get("sub") == TEST_USERNAME
    except JWTError:
        pytest.fail("JWT decoding failed for a valid token.")

def test_token_expiry():
    """
    Tests that the token expiry is set correctly.
    """
    expires_delta = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    token = security.create_access_token(data={"sub": TEST_USERNAME}, expires_delta=expires_delta)
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    
    assert "exp" in payload
    # Add a small buffer for timing differences
    expected_expiry = payload.get("exp")
    assert expected_expiry is not None
    # Cannot reliably test exact time, but can check it's in the future

def test_get_current_user_valid_token():
    """
    Tests that `get_current_user` dependency correctly decodes a valid token.
    This is a conceptual test as `get_current_user` needs a DB session.
    We are testing the token decoding part that `get_current_user` relies on.
    """
    token = security.create_access_token(data={"sub": TEST_USERNAME})
    
    # Simulate the dependency call by manually decoding
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str | None = payload.get("sub")
        assert username == TEST_USERNAME
    except JWTError:
        pytest.fail("Valid token failed to decode.")

def test_get_current_user_invalid_token():
    """
    Tests that `get_current_user` dependency handles an invalid token.
    """
    invalid_token = "this.is.an.invalid.token"
    with pytest.raises(JWTError):
        jwt.decode(invalid_token, SECRET_KEY, algorithms=[ALGORITHM])

def test_get_current_user_malformed_token():
    """
    Tests that `get_current_user` dependency handles a token with missing 'sub'.
    """
    token_no_sub = security.create_access_token(data={"user": "testuser"})
    with pytest.raises(JWTError):
        payload = jwt.decode(token_no_sub, SECRET_KEY, algorithms=[ALGORITHM])
        # The dependency itself would raise credentials_exception,
        # here we test the underlying reason it would fail.
        if payload.get("sub") is None:
            raise JWTError("Missing 'sub' claim") 