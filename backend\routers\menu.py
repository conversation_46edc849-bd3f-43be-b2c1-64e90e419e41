from typing import List, Set
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from .. import schemas, services, models
from ..database import get_db
from ..security import get_current_active_user

router = APIRouter(
    prefix="/api/menus",
    tags=["menus"],
)

@router.post("/", response_model=schemas.Menu, status_code=status.HTTP_201_CREATED)
def create_menu(menu: schemas.MenuCreate, db: Session = Depends(get_db)):
    return services.create_menu(db=db, menu=menu)

@router.get("/", response_model=List[schemas.Menu])
def read_menus(db: Session = Depends(get_db)):
    """
    获取所有菜单项，通常用于菜单管理界面。
    """
    menus = services.get_menus(db)
    return menus

@router.get("/my-menus", response_model=List[schemas.Menu])
def get_my_menus(db: Session = Depends(get_db), current_user: schemas.User = Depends(get_current_active_user)):
    """
    核心: 获取当前用户有权访问的菜单列表。
    """
    # 如果是超级管理员，直接返回所有菜单
    is_admin = any(role.name == "Admin" for role in current_user.roles)
    if is_admin:
        return services.get_menus(db)

    user_permissions: Set[str] = set()
    for role in current_user.roles:
        for permission in role.permissions:
            user_permissions.add(permission.name)
    
    # 过滤出带有 ':view' 的权限，并提取其菜单 key
    viewable_menu_keys: Set[str] = set()
    for perm_name in user_permissions:
        if perm_name.endswith(':view'):
            menu_key = perm_name.split(':')[0]
            viewable_menu_keys.add(menu_key)

    all_menus = services.get_menus(db=db)
    
    visible_menus = [menu for menu in all_menus if menu.key in viewable_menu_keys]

    return visible_menus

@router.put("/{menu_id}", response_model=schemas.Menu)
def update_menu(menu_id: int, menu: schemas.MenuUpdate, db: Session = Depends(get_db)):
    db_menu = services.update_menu(db, menu_id=menu_id, menu_update=menu)
    if db_menu is None:
        raise HTTPException(status_code=404, detail="Menu not found")
    return db_menu

@router.delete("/{menu_id}", response_model=schemas.Menu)
def delete_menu(menu_id: int, db: Session = Depends(get_db)):
    db_menu = services.delete_menu(db, menu_id=menu_id)
    if db_menu is None:
        raise HTTPException(status_code=404, detail="Menu not found")
    return db_menu 