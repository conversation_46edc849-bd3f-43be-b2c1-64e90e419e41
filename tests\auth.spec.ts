import { test, expect } from '@playwright/test';

test.describe('Authentication Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到登录页面
    await page.goto('/login');
  });

  test('should display login form', async ({ page }) => {
    // 检查登录表单元素是否存在
    await expect(page.locator('input[placeholder*="用户名"], input[placeholder*="Username"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("登录"), button:has-text("Login")')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    // 输入错误的凭据
    await page.fill('input[placeholder*="用户名"], input[placeholder*="Username"]', 'wronguser');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"], button:has-text("登录"), button:has-text("Login")');

    // 等待错误消息出现
    await expect(page.locator('.ant-message-error, .error-message')).toBeVisible();
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    // 输入正确的管理员凭据
    await page.fill('input[placeholder*="用户名"], input[placeholder*="Username"]', 'admin');
    await page.fill('input[type="password"]', 'adminpassword');
    await page.click('button[type="submit"], button:has-text("登录"), button:has-text("Login")');

    // 等待重定向到主页面
    await page.waitForURL('**/users');
    
    // 检查是否成功登录（应该看到用户管理页面）
    await expect(page.locator('h1, .ant-typography-title')).toContainText(['用户管理', 'User Management']);
  });

  test('should logout successfully', async ({ page }) => {
    // 先登录
    await page.fill('input[placeholder*="用户名"], input[placeholder*="Username"]', 'admin');
    await page.fill('input[type="password"]', 'adminpassword');
    await page.click('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    
    // 等待登录完成
    await page.waitForURL('**/users');
    
    // 点击登出按钮
    await page.click('button:has-text("登出"), button:has-text("Logout"), .ant-btn:has(.anticon-logout)');
    
    // 应该重定向回登录页面
    await page.waitForURL('**/login');
    await expect(page.locator('input[placeholder*="用户名"], input[placeholder*="Username"]')).toBeVisible();
  });
});
