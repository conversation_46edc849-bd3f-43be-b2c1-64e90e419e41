# 项目 API 文档

本文档基于后端代码自动生成，提供了所有可用 API 端点的详细说明。所有 API 的基础路径为 `/api`。

**通用约定:**
- **认证:** 需要认证的接口会在说明中标注 `[需认证]`。请求时必须在 Header 中携带 `Authorization: Bearer <your_token>`。
- **分页:** 对于返回列表的 GET 请求，通常支持以下查询参数：
    - `skip` (integer, default: 0): 跳过的记录数。
    - `limit` (integer, default: 100): 返回的最大记录数。
- **错误响应:**
    - `401 Unauthorized`: Token 无效或未提供。
    - `404 Not Found`: 请求的资源不存在。
    - `422 Unprocessable Entity`: 请求的数据验证失败。

---

## 1. 认证模块 (`/auth`)

负责处理用户登录、Token 生成和身份验证。

### **POST** `/auth/token`

**功能:** 用户登录以获取访问令牌。
**描述:** 使用用户名和密码进行身份验证。成功后返回 JWT Token 和完整的用户信息。
**请求体 (`application/x-www-form-urlencoded`):**
- `username` (string, required): 用户名。
- `password` (string, required): 密码。
**成功响应 (200):**
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "user": {
    "id": 0,
    "username": "string",
    "email": "<EMAIL>",
    "full_name": "string",
    "is_active": true,
    "role": {
      "id": 0,
      "name": "string",
      "description": "string",
      "permissions": [
        {
          "id": 0,
          "name": "string",
          "description": "string"
        }
      ]
    }
  }
}
```

### **GET** `/auth/me`

**功能:** 获取当前登录用户的信息。 `[需认证]`
**成功响应 (200):**
- 返回一个 `User` 对象，结构同上。

### **GET** `/auth/me/permissions`

**功能:** 获取当前登录用户的权限标识符列表。 `[需认证]`
**成功响应 (200):**
- 返回一个字符串数组。
```json
[
  "user:create",
  "user:read",
  "role:read"
]
```

---

## 2. 用户管理 (`/users`)

负责用户的增、删、改、查。

### **POST** `/users/`

**功能:** 创建一个新用户。
**请求体 (`application/json`):**
```json
{
  "username": "string",
  "email": "<EMAIL>",
  "full_name": "string",
  "is_active": true,
  "password": "string",
  "role_id": 0
}
```
**成功响应 (200):**
- 返回新创建的 `User` 对象。

### **GET** `/users/stats/`

**功能:** 获取用户列表及统计信息。
**查询参数:** `skip`, `limit`
**成功响应 (200):**
```json
{
  "users": [ /* User 列表 */ ],
  "stats": {
    "total": 0,
    "active": 0,
    "by_role": {
      "Admin": 0,
      "User": 0
    }
  }
}
```

### **GET** `/users/`

**功能:** 获取用户列表（分页）。
**查询参数:** `skip`, `limit`
**成功响应 (200):**
- 返回 `User` 对象数组。

### **GET** `/users/{user_id}`

**功能:** 获取指定 ID 的用户信息。
**成功响应 (200):**
- 返回一个 `User` 对象。

### **PUT** `/users/{user_id}`

**功能:** 更新指定 ID 的用户信息。
**请求体 (`application/json`):**
```json
{
  "username": "string",
  "email": "<EMAIL>",
  "full_name": "string",
  "is_active": true,
  "password": "string", // 可选，仅在需要修改密码时提供
  "role_id": 0
}
```
**成功响应 (200):**
- 返回更新后的 `User` 对象。

---

## 3. 角色管理 (`/roles`)

负责角色和权限分配的管理。

### **POST** `/roles/`

**功能:** 创建一个新角色。
**请求体 (`application/json`):**
```json
{
  "name": "string",
  "description": "string"
}
```
**成功响应 (200):**
- 返回新创建的 `Role` 对象。

### **GET** `/roles/stats/`

**功能:** 获取角色列表及统计信息（如每个角色的用户数）。
**查询参数:** `skip`, `limit`
**成功响应 (200):**
```json
{
  "roles": [
    {
      "id": 0,
      "name": "string",
      "description": "string",
      "permissions": [ /* Permission 列表 */ ],
      "user_count": 0
    }
  ],
  "stats": { /* 统计对象 */ }
}
```

### **GET** `/roles/`

**功能:** 获取角色列表（分页）。
**查询参数:** `skip`, `limit`
**成功响应 (200):**
- 返回 `Role` 对象数组。

### **GET** `/roles/{role_id}`

**功能:** 获取指定 ID 的角色信息。
**成功响应 (200):**
- 返回一个 `Role` 对象。

### **PUT** `/roles/{role_id}`

**功能:** 更新指定 ID 的角色信息。
**请求体 (`application/json`):**
```json
{
  "name": "string",
  "description": "string"
}
```
**成功响应 (200):**
- 返回更新后的 `Role` 对象。

### **DELETE** `/roles/{role_id}`

**功能:** 删除指定 ID 的角色。
**成功响应 (200):**
- 返回被删除的 `Role` 对象。

### **PUT** `/roles/{role_id}/permissions`

**功能:** 更新角色的权限分配。
**描述:** 传入一个包含所有要分配给该角色的权限 ID 的列表。
**请求体 (`application/json`):**
```json
{
  "permission_ids": [1, 2, 3]
}
```
**成功响应 (200):**
- 返回更新后的 `Role` 对象（包含最新的权限列表）。

---

## 4. 权限管理 (`/permissions`)

负责权限的查询。

### **GET** `/permissions/`

**功能:** 获取所有权限的列表。
**查询参数:** `skip`, `limit`
**成功响应 (200):**
- 返回 `Permission` 对象数组。

---

## 5. 菜单管理 (`/menus`)

负责菜单的增删改查和权限控制。

### **GET** `/menus/`

**功能:** 获取完整的菜单树结构。
**成功响应 (200):**
- 返回一个 `Menu` 对象数组，每个对象可能包含 `children` 字段，形成树状结构。

### **GET** `/menus/me`

**功能:** 获取当前登录用户可访问的菜单树。 `[需认证]`
**描述:** 根据用户的角色权限，过滤并返回该用户有权查看的菜单。
**成功响应 (200):**
- 返回过滤后的 `Menu` 对象数组（树状结构）。

### **POST** `/menus/`

**功能:** 创建一个新的菜单项。 `[需认证]`
**请求体 (`application/json`):**
```json
{
  "name": "string",
  "path": "string",
  "icon": "string",
  "order": 0,
  "parent_id": 0 // 可选，顶级菜单则省略
}
```
**成功响应 (201):**
- 返回新创建的 `Menu` 对象。

### **PUT** `/menus/{menu_id}`

**功能:** 更新指定 ID 的菜单项。 `[需认证]`
**请求体 (`application/json`):**
- 结构同上 `POST /menus/`。
**成功响应 (200):**
- 返回更新后的 `Menu` 对象。

### **DELETE** `/menus/{menu_id}`

**功能:** 删除指定 ID 的菜单项。 `[需认证]`
**成功响应 (200):**
- 返回被删除的 `Menu` 对象。