# 项目 RBAC 与核心功能设计文档 (V2.4 Final Corrected)

## 第1部分：RBAC (基于角色的访问控制) 设计

### 1.1 设计目标
- **核心思想**: **“用户(User)通过扮演角色(Role)来获得操作权限(Permission)。”**
- **目标**: 实现授权管理的简化、最小权限原则、业务与权限解耦，并支持前端UI的动态构建。

### 1.2 详细数据模型设计 (Backend Data Models)
为了便于追踪和管理，所有核心业务表都包含一套通用的审计字段。

#### 1.2.1 通用审计字段 (Common Audit Fields)
- `created_at` (TIMESTAMP WITH TIME ZONE, NOT NULL, default: now()): 记录创建时间。
- `created_by` (VARCHAR(50), NULLABLE): 记录创建该记录的用户名。
- `updated_at` (TIMESTAMP WITH TIME ZONE, NOT NULL, default: now()): 记录最后修改时间，在更新时自动更新。
- `updated_by` (VARCHAR(50), NULLABLE): 记录最后修改该记录的用户名。
- `is_deleted` (BOOLEAN, NOT NULL, default: false): 逻辑删除标记，用于软删除。
- `status` (VARCHAR(20), NOT NULL, default: 'active'): 记录状态，如 'active', 'inactive', 'pending'。

#### 1.2.2 核心表模型
- **`users` (用户表)**
  - `id` (BIGINT, PK, Auto-increment)
  - `username` (VARCHAR(50), NOT NULL, UNIQUE): 用户登录名。
  - `hashed_password` (VARCHAR(255), NOT NULL): BCrypt 哈希后的密码。
  - `email` (VARCHAR(100), NOT NULL, UNIQUE): 用户邮箱。
  - `full_name` (VARCHAR(100), NULLABLE): 用户全名或昵称。
  - `avatar_url` (VARCHAR(255), NULLABLE): 用户头像URL。
  - `role_id` (BIGINT, FK -> `roles.id`, NOT NULL): **核心关联**，每个用户仅属于一个角色。
  - `last_login_at` (TIMESTAMP WITH TIME ZONE, NULLABLE): 最后登录时间。
  - *通用审计字段...*

- **`roles` (角色表)**
  - `id` (BIGINT, PK, Auto-increment)
  - `name` (VARCHAR(50), NOT NULL, UNIQUE): 角色名 (英文，程序用，如 "Admin")。
  - `display_name` (VARCHAR(100), NOT NULL): 角色显示名 (中文，UI用，如 "系统管理员")。
  - `description` (TEXT, NULLABLE): 角色描述。
  - *通用审计字段...*

- **`permissions` (权限表)**
  - `id` (BIGINT, PK, Auto-increment)
  - `name` (VARCHAR(100), NOT NULL, UNIQUE): **权限唯一标识符**，遵循 `{resource}:{action}` 规范，如 `system_user:create`。
  - `description` (VARCHAR(255), NOT NULL): 人性化描述，如 "用户管理 - 创建用户"。
  - `category` (VARCHAR(50), NOT NULL): 权限分类，用于UI分组展示，如 "用户管理"。

- **`role_permissions` (角色-权限关联表)** - **多对多关联**
  - `role_id` (BIGINT, PK, FK -> `roles.id`)
  - `permission_id` (BIGINT, PK, FK -> `permissions.id`)

- **`menus` (菜单表)**
  - `id` (BIGINT, PK, Auto-increment)
  - `parent_id` (BIGINT, FK -> `menus.id`, NULLABLE): 父菜单ID，用于构建树状结构。
  - `path` (VARCHAR(255), NOT NULL, UNIQUE): 前端路由路径，如 `/system/user-management`。
  - `name` (VARCHAR(100), NOT NULL): 菜单显示名称。
  - `icon` (VARCHAR(50), NULLABLE): Ant Design 的图标名。
  - `order_num` (INT, NOT NULL, default: 0): 排序号，数字越小越靠前。
  - `is_visible` (BOOLEAN, NOT NULL, default: true): 控制菜单是否在UI中默认显示。
  - *通用审计字段...*

### 1.3 权限与菜单联动生成机制

这是一个核心的自动化流程，旨在简化管理，确保菜单与权限的同步。

**流程说明**: 当一个**菜单**被创建或其关键信息（如路径）被更新时，**后端必须自动地为其创建或更新一套关联的基础操作权限**。

1.  **触发时机**:
    -   当调用 `POST /api/menus/` (创建新菜单) 成功时。
    -   当调用 `PUT /api/menus/{id}` (更新菜单) 且菜单的 `path` 字段发生改变时。

2.  **权限命名规范**:
    -   **资源标识符 (`resource_identifier`)**: 由菜单的 `path` 字段转化而来（去除首部`/`，并将所有`/`和`-`替换为`_`）。例如，路径 `/system/user-management` -> 资源标识符 `system_user_management`。
    -   **基础操作 (`actions`)**: 系统预定义一套标准操作动词，至少包括：`view`, `create`, `edit`, `delete`。
    -   **权限全名 (`permission.name`)**: 遵循 `{resource_identifier}:{action}` 格式。

3.  **后端自动化逻辑 (在创建/更新菜单的 Service 层实现)**:
    -   **Step 1**: 成功在 `menus` 表中创建或更新一条菜单记录后，从该记录中获取其 `path` 和 `name`。
    -   **Step 2**: 根据 `path` 生成 `resource_identifier`。
    -   **Step 3**: 遍历预定义的基础操作动词列表 (`['view', 'create', 'edit', 'delete']`)。
    -   **Step 4**: 对于每一个 `action`：
        -   生成权限全名，如 `system_user_management:create`。
        -   生成权限描述，如 `用户管理 - 创建` (由菜单的 `name` + 操作的中文描述构成)。
        -   生成权限分类，直接使用菜单的 `name`，如 `用户管理`。
        -   **检查 `permissions` 表中是否存在同名的权限**。
        -   如果**不存在**，则向 `permissions` 表中**插入**这条新的权限记录。
        -   如果**存在**（可能在更新菜单路径时发生），则可以选择更新其描述和分类。
    -   **Step 5 (可选但推荐)**: 创建新菜单后，可以默认将新生成的 `view` 权限自动授予“系统管理员”角色，确保管理员立即可见。

4.  **前端交互**:
    -   前端在“菜单管理”页面创建或编辑菜单时，只需提交菜单本身的信息（如名称、路径、父节点等）。
    -   前端**无需**也不能手动管理权限的生成。用户应该感知到的是：**一旦一个菜单被创建，其对应的“查看、新建、编辑、删除”等基础权限就自动可用了**，并可以在“角色管理”的权限树中看到它们。

### 1.4 授权与鉴权流程 (Authorization & Authentication Flow)

#### 1.4.1 授权管理 (Authorization Management)
-   在“角色管理”页面，为角色分配权限。UI以权限树的形式展示所有权限，管理员通过勾选为角色授权。

#### 1.4.2 鉴权流程 (Permission Check Flow)
-   **后端强制鉴权**: 每个API端点通过依赖注入进行严格的权限校验，检查当前用户角色是否拥有执行该操作所需的权限。
-   **前端UI辅助控制**: 登录时调用 `/api/menus/me` 获取用户专属的菜单树和**操作权限列表 (`actions`)**，前端据此动态渲染UI，对无权操作的按钮进行禁用或隐藏。

---

## 第2部分：前端核心功能页面UI/UX设计规约

### 2.1 用户管理页面 (`/system/user-management`)
- **权限要求**: `system_user:view` (页面访问), `system_user:create` (新建按钮) 等。
- **UI 元素**:
  - **搜索/过滤区**: 按“用户名/邮箱”、“角色”、“状态”进行筛选。
  - **操作区**: “新建用户”按钮。
  - **数据展示区**:
    - `Table`: 列包括 `ID`, `用户名`, `邮箱`, `全名`, `角色` (`Tag`), `状态` (`Badge`), `最后登录时间`, `创建时间`, `操作`。
    - **分页**: 集成 `Pagination` 组件。
- **交互逻辑**:
  - **新建/编辑**: 通过模态框表单完成，表单含格式校验。
  - **删除**: 使用 `Popconfirm` 进行二次确认。

### 2.2 角色管理页面 (`/system/role-management`)
- **权限要求**: `system_role:view`, `system_role:create`, `system_role:assign_permissions` 等。
- **UI 元素**:
  - **操作区**: “新建角色”按钮。
  - **数据展示区**: `Table`，列包括 `ID`, `角色名`, `角色显示名`, `状态`, `描述`, `操作`。
  - **操作列**: “编辑”、“删除”、“分配权限”三个链接按钮。
- **交互逻辑**:
  - **新建/编辑/删除**: 交互与用户管理类似。若角色下仍有用户，则禁止删除，并给出明确提示。
  - **分配权限**:
    - 点击“分配权限”按钮，弹出标题为 **“为「角色显示名」分配权限”** 的模态框。
    - 模态框主体为可勾选的 `Tree` 组件，按 `category` 分组展示所有权限。
    - `Tree` 组件需开启 `checkable` 属性，并支持父子节点关联选择。
    - 进入时，根据角色已有权限自动勾选节点。
    - 点击模态框的“确定”按钮，前端将所有被勾选的权限ID（**包括因勾选子节点而处于半选状态的父节点ID**）完整地提交给后端。

### 2.3 菜单管理页面 (`/system/menu-management`)
- **权限要求**: `system_menu:view`, `system_menu:create` 等。
- **UI 元素**:
  - **数据展示区**: 使用 `Table` 组件，并开启**树状数据展示**功能，直观地展示菜单的层级结构。
  - **列**: `菜单名称` (带缩进), `图标` (渲染图标), `路由路径`, `排序号`, `状态`, `操作`。
  - **操作列**: “新建子菜单”、“编辑”、“删除”。
- **交互逻辑**:
  - **新建/编辑**: 使用模态框表单，表单项包括父级菜单（树形选择器）、名称、路径、图标（可选，可提供图标选择器）、排序号等。**用户完成此操作后，后端应按 1.3 节的机制自动处理关联权限的生成**。