import React, { useMemo, useState } from 'react';
import { Outlet, useLocation, useNavigate, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import type { UseQueryResult } from '@tanstack/react-query';
import { Layout, Menu, Breadcrumb, Spin, Alert, Button, theme } from 'antd';
import type { MenuProps } from 'antd';
import { LogoutOutlined, HomeOutlined } from '@ant-design/icons';
import apiClient from '../services/api';
import type { Menu as MenuType } from '../types/entities';
import useAuthStore from '../store/authStore';

const { Header, Content, Footer, Sider } = Layout;

const fetchUserMenus = async () => {
    const { data } = await apiClient.get<MenuType[]>('/api/menus/my-menus');
    return data;
};

// 辅助函数：将扁平的菜单列表转换为树形结构
const buildMenuTree = (menus: MenuType[], parentId: number | null = null): MenuType[] => {
    const children = menus.filter(menu => menu.parent_id === parentId);
    return children.map(child => ({
        ...child,
        children: buildMenuTree(menus, child.id),
    }));
};

// 辅助函数：将树形菜单渲染为 Antd Menu Items
const renderMenuItems = (menus: MenuType[]): NonNullable<MenuProps['items']> => {
    return menus.map((menu) => ({
        key: menu.path,
        icon: menu.icon ? <HomeOutlined /> : null, // 待实现动态图标
        label: menu.children && menu.children.length > 0 ? menu.display_name : <Link to={menu.path}>{menu.display_name}</Link>,
        children: menu.children && menu.children.length > 0 ? renderMenuItems(menu.children) : undefined,
    }));
};

const MainLayout: React.FC = () => {
    const [collapsed, setCollapsed] = useState(false);
    const {
        token: { colorBgContainer },
    } = theme.useToken();

    const navigate = useNavigate();
    const location = useLocation();
    const logout = useAuthStore(state => state.logout);

    const { data: menuData, isLoading, isError, error }: UseQueryResult<MenuType[], Error> = useQuery({
        queryKey: ['myMenus'],
        queryFn: fetchUserMenus,
    });
    
    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    const handleMenuClick: MenuProps['onClick'] = (e) => {
        navigate(e.key);
    };

    const menuTree = useMemo(() => (menuData ? buildMenuTree(menuData) : []), [menuData]);
    const menuItems = useMemo(() => (menuTree ? renderMenuItems(menuTree) : []), [menuTree]);
    
    // 面包屑逻辑
    const breadcrumbItems = useMemo(() => {
        const pathSnippets = location.pathname.split('/').filter(i => i);
        const breadcrumbs = pathSnippets.map((_, index) => {
            const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
            const menuItem = menuData?.find(item => item.path === url);
            return {
                key: url,
                title: menuItem?.display_name || 'Detail',
            };
        });

        return [
            {
                key: '/',
                title: <Link to="/"><HomeOutlined /></Link>,
            },
            ...breadcrumbs,
        ].map(item => ({...item, title: <Link to={item.key}>{item.title}</Link>}));
    }, [location.pathname, menuData]);


    if (isLoading) {
        return <Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }} />;
    }

    if (isError) {
        return <Alert message="Error" description={error.message} type="error" showIcon />;
    }
    
    const selectedKeys = [location.pathname];
    const defaultOpenKeys = location.pathname.split('/').slice(0, 2).join('/');


    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Sider collapsible collapsed={collapsed} onCollapse={setCollapsed}>
                <div style={{ height: '32px', margin: '16px', background: 'rgba(255, 255, 255, 0.2)', textAlign: 'center', lineHeight: '32px', color: 'white' }}>Logo</div>
                <Menu
                    theme="dark"
                    mode="inline"
                    items={menuItems}
                    selectedKeys={selectedKeys}
                    defaultOpenKeys={[defaultOpenKeys]}
                    onClick={handleMenuClick}
                />
            </Sider>
            <Layout>
                <Header style={{ padding: '0 16px', background: colorBgContainer, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Breadcrumb items={breadcrumbItems} />
                    <Button icon={<LogoutOutlined />} onClick={handleLogout}>
                        Logout
                    </Button>
                </Header>
                <Content style={{ margin: '16px' }}>
                    <div style={{ padding: 24, minHeight: 360, background: colorBgContainer }}>
                        <Outlet />
                    </div>
                </Content>
                <Footer style={{ textAlign: 'center' }}>
                    Ant Design ©2023 Created by Ant UED
                </Footer>
            </Layout>
        </Layout>
    );
};

export default MainLayout; 