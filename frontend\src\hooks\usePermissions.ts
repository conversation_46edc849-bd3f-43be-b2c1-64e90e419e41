import { useMemo } from 'react';
import useAuthStore from '../store/authStore';
import type { Role, Permission } from '../types/entities';

export const usePermissions = () => {
    const user = useAuthStore((state: any) => state.user);

    const permissionsSet = useMemo(() => {
        const allPermissions = new Set<string>();
        if (user && user.roles) {
            user.roles.forEach((role: Role) => {
                role.permissions.forEach((permission: Permission) => {
                    allPermissions.add(permission.name);
                });
            });
        }
        return allPermissions;
    }, [user]);

    const hasPermission = (requiredPermission: string): boolean => {
        return permissionsSet.has(requiredPermission);
    };

    return { permissions: permissionsSet, hasPermission };
}; 