import requests
import json

BASE_URL = "http://localhost:8000"

def print_step(title):
    print(f"\n{'='*20}\n[TEST STEP] {title}\n{'='*20}")

def print_result(response):
    try:
        print(f"Status Code: {response.status_code}")
        print("Response JSON:")
        print(json.dumps(response.json(), indent=2))
    except json.JSONDecodeError:
        print("Response Text:")
        print(response.text)

def test_login():
    print_step("Admin Login")
    url = f"{BASE_URL}/api/auth/token"
    data = {"username": "admin", "password": "adminpassword"}
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    
    # Using requests with data payload for urlencoded form
    response = requests.post(url, data=data) # requests handles urlencoding
    
    print_result(response)
    
    if response.status_code == 200:
        print("\n[SUCCESS] Admin login successful.")
        return response.json()["access_token"]
    else:
        print("\n[FAILURE] Admin login failed.")
        return None

if __name__ == "__main__":
    admin_token = test_login()
    if not admin_token:
        exit(1) 