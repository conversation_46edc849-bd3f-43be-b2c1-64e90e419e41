import { test, expect } from '@playwright/test';

test.describe('API Tests', () => {
  let authToken: string;

  test.beforeAll(async ({ request }) => {
    // 获取认证token
    const loginResponse = await request.post('http://localhost:8000/api/auth/token', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: 'username=admin&password=adminpassword',
    });

    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    authToken = loginData.access_token;
    expect(authToken).toBeTruthy();
  });

  test('should authenticate successfully', async ({ request }) => {
    const response = await request.post('http://localhost:8000/api/auth/token', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: 'username=admin&password=adminpassword',
    });

    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data.access_token).toBeTruthy();
    expect(data.token_type).toBe('bearer');
    expect(data.user).toBeTruthy();
    expect(data.user.username).toBe('admin');
  });

  test('should fail authentication with wrong credentials', async ({ request }) => {
    const response = await request.post('http://localhost:8000/api/auth/token', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: 'username=wronguser&password=wrongpassword',
    });

    expect(response.status()).toBe(401);
  });

  test('should get current user info', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    expect(response.ok()).toBeTruthy();
    const user = await response.json();
    expect(user.username).toBe('admin');
    expect(user.email).toBe('<EMAIL>');
  });

  test('should get users list', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/users/', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    expect(response.ok()).toBeTruthy();
    const users = await response.json();
    expect(Array.isArray(users)).toBeTruthy();
    expect(users.length).toBeGreaterThan(0);
  });

  test('should create a new user', async ({ request }) => {
    const timestamp = Date.now();
    const newUser = {
      username: `apiuser${timestamp}`,
      email: `apiuser${timestamp}@example.com`,
      password: 'testpassword123',
      full_name: 'API Test User',
      role_ids: [2] // 假设角色ID 2 是普通用户角色
    };

    const response = await request.post('http://localhost:8000/api/users/', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
      data: newUser,
    });

    expect(response.ok()).toBeTruthy();
    const createdUser = await response.json();
    expect(createdUser.username).toBe(newUser.username);
    expect(createdUser.email).toBe(newUser.email);
    expect(createdUser.full_name).toBe(newUser.full_name);
  });

  test('should get roles list', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/roles/', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    expect(response.ok()).toBeTruthy();
    const roles = await response.json();
    expect(Array.isArray(roles)).toBeTruthy();
    expect(roles.length).toBeGreaterThan(0);
    
    // 检查是否有Admin角色
    const adminRole = roles.find((role: any) => role.name === 'Admin');
    expect(adminRole).toBeTruthy();
  });

  test('should get menus list', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/menus/', {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    expect(response.ok()).toBeTruthy();
    const menus = await response.json();
    expect(Array.isArray(menus)).toBeTruthy();
  });

  test('should require authentication for protected endpoints', async ({ request }) => {
    const response = await request.get('http://localhost:8000/api/users/');
    expect(response.status()).toBe(401);
  });
});
