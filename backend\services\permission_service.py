from sqlalchemy.orm import Session
from .. import models

def get_permission(db: Session, permission_id: int):
    """
    通过 ID 获取单个权限
    """
    return db.query(models.Permission).filter(models.Permission.id == permission_id).first()

def get_permission_by_name(db: Session, name: str):
    """
    通过 name 获取单个权限
    """
    return db.query(models.Permission).filter(models.Permission.name == name).first()

def get_permissions(db: Session, skip: int = 0, limit: int = 100):
    """
    获取权限列表（分页）
    """
    return db.query(models.Permission).offset(skip).limit(limit).all()

def get_permissions_by_menu_id(db: Session, menu_id: int):
    """
    获取某个菜单关联的所有权限
    """
    return db.query(models.Permission).filter(models.Permission.menu_id == menu_id).all() 