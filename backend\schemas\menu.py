from pydantic import BaseModel
from typing import Optional, List

class MenuBase(BaseModel):
    key: str
    display_name: str
    path: str
    parent_id: Optional[int] = None
    order: int
    icon: Optional[str] = None

class MenuCreate(MenuBase):
    pass

class MenuUpdate(BaseModel):
    key: Optional[str] = None
    display_name: Optional[str] = None
    path: Optional[str] = None
    parent_id: Optional[int] = None
    order: Optional[int] = None
    icon: Optional[str] = None

class Menu(MenuBase):
    id: int
    children: List['Menu'] = []

    class Config:
        from_attributes = True

Menu.model_rebuild() 