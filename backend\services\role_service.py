from typing import List
from sqlalchemy.orm import Session
from .. import models, schemas

def get_role(db: Session, role_id: int):
    """
    通过 ID 获取单个角色
    """
    return db.query(models.Role).filter(models.Role.id == role_id).first()

def get_role_by_name(db: Session, name: str):
    """
    通过名称获取单个角色
    """
    return db.query(models.Role).filter(models.Role.name == name).first()

def get_roles(db: Session, skip: int = 0, limit: int = 100):
    """
    获取角色列表（分页）
    """
    return db.query(models.Role).offset(skip).limit(limit).all()

def create_role(db: Session, role: schemas.RoleCreate):
    """
    创建新角色
    """
    db_role = models.Role(name=role.name, display_name=role.display_name)
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    return db_role

def update_role(db: Session, role_id: int, role_update: schemas.RoleUpdate):
    """
    更新角色信息
    """
    db_role = get_role(db, role_id)
    if db_role:
        update_data = role_update.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_role, key, value)
        db.commit()
        db.refresh(db_role)
    return db_role

def delete_role(db: Session, role_id: int):
    """
    删除角色
    """
    db_role = get_role(db, role_id)
    if db_role:
        db.delete(db_role)
        db.commit()
    return db_role

def assign_permissions_to_role(db: Session, role_id: int, permission_ids: List[int]):
    """
    为角色分配权限
    """
    db_role = get_role(db, role_id)
    if not db_role:
        return None

    # 查询所有有效的权限对象
    permissions = db.query(models.Permission).filter(models.Permission.id.in_(permission_ids)).all()
    
    # 直接替换为新的权限列表
    db_role.permissions = permissions
    
    db.commit()
    db.refresh(db_role)
    return db_role 