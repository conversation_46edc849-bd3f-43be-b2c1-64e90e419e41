#!/usr/bin/env node

/**
 * 简单的Playwright MCP服务器
 * 为Augment提供Playwright自动化测试功能
 */

const { spawn } = require('child_process');
const path = require('path');

class PlaywrightMCPServer {
  constructor() {
    this.tools = [
      {
        name: 'run_playwright_test',
        description: '运行Playwright测试',
        inputSchema: {
          type: 'object',
          properties: {
            testFile: {
              type: 'string',
              description: '测试文件路径'
            },
            headed: {
              type: 'boolean',
              description: '是否显示浏览器界面',
              default: false
            }
          },
          required: ['testFile']
        }
      },
      {
        name: 'create_playwright_test',
        description: '创建新的Playwright测试文件',
        inputSchema: {
          type: 'object',
          properties: {
            fileName: {
              type: 'string',
              description: '测试文件名'
            },
            testContent: {
              type: 'string',
              description: '测试内容'
            }
          },
          required: ['fileName', 'testContent']
        }
      },
      {
        name: 'playwright_codegen',
        description: '启动Playwright代码生成器',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: '要录制的网站URL',
              default: 'http://localhost:5174'
            }
          }
        }
      }
    ];
  }

  async handleRequest(request) {
    const { method, params } = request;

    switch (method) {
      case 'initialize':
        return {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          serverInfo: {
            name: 'playwright-mcp-server',
            version: '1.0.0'
          }
        };

      case 'tools/list':
        return { tools: this.tools };

      case 'tools/call':
        return await this.callTool(params.name, params.arguments);

      default:
        throw new Error(`Unknown method: ${method}`);
    }
  }

  async callTool(toolName, args) {
    switch (toolName) {
      case 'run_playwright_test':
        return await this.runPlaywrightTest(args);
      
      case 'create_playwright_test':
        return await this.createPlaywrightTest(args);
      
      case 'playwright_codegen':
        return await this.playwrightCodegen(args);
      
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  async runPlaywrightTest(args) {
    const { testFile, headed = false } = args;
    
    return new Promise((resolve, reject) => {
      const command = 'npx';
      const cmdArgs = ['playwright', 'test', testFile];
      
      if (headed) {
        cmdArgs.push('--headed');
      }

      const child = spawn(command, cmdArgs, {
        cwd: process.cwd(),
        stdio: 'pipe'
      });

      let output = '';
      let error = '';

      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      child.stderr.on('data', (data) => {
        error += data.toString();
      });

      child.on('close', (code) => {
        resolve({
          content: [
            {
              type: 'text',
              text: `测试执行完成 (退出码: ${code})\n\n输出:\n${output}\n\n错误:\n${error}`
            }
          ]
        });
      });

      child.on('error', (err) => {
        reject(new Error(`执行测试失败: ${err.message}`));
      });
    });
  }

  async createPlaywrightTest(args) {
    const { fileName, testContent } = args;
    const fs = require('fs').promises;
    const testPath = path.join('tests', fileName);

    try {
      await fs.mkdir('tests', { recursive: true });
      await fs.writeFile(testPath, testContent);
      
      return {
        content: [
          {
            type: 'text',
            text: `测试文件已创建: ${testPath}`
          }
        ]
      };
    } catch (error) {
      throw new Error(`创建测试文件失败: ${error.message}`);
    }
  }

  async playwrightCodegen(args) {
    const { url = 'http://localhost:5174' } = args;
    
    return new Promise((resolve) => {
      const child = spawn('npx', ['playwright', 'codegen', url], {
        cwd: process.cwd(),
        stdio: 'inherit'
      });

      child.on('close', (code) => {
        resolve({
          content: [
            {
              type: 'text',
              text: `Playwright代码生成器已启动，访问 ${url}`
            }
          ]
        });
      });
    });
  }
}

// 启动MCP服务器
const server = new PlaywrightMCPServer();

process.stdin.on('data', async (data) => {
  try {
    const request = JSON.parse(data.toString());
    const response = await server.handleRequest(request);
    
    console.log(JSON.stringify({
      jsonrpc: '2.0',
      id: request.id,
      result: response
    }));
  } catch (error) {
    console.log(JSON.stringify({
      jsonrpc: '2.0',
      id: request.id || null,
      error: {
        code: -1,
        message: error.message
      }
    }));
  }
});

console.error('Playwright MCP Server started');
