import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Table, Button, Modal, Form, Input, message, Popconfirm, Tag, Space, Tree, Spin } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { DataNode } from 'antd/es/tree';
import { PlusOutlined, EditOutlined, DeleteOutlined, KeyOutlined } from '@ant-design/icons';
import { getRoles, createRole, updateRole, deleteRole, getPermissions, assignPermissionsToRole } from '../services/api';
import type { Role, Permission, Menu } from '../types/entities';
import type { RoleCreate, RoleUpdate } from '../services/types';

// Helper to build a permission tree grouped by menu
const buildPermissionTree = (permissions: Permission[], menus: Menu[]): DataNode[] => {
    const menuMap = new Map<number, DataNode & { children: DataNode[] }>();
    menus.forEach(menu => {
        menuMap.set(menu.id, {
            key: `menu-${menu.id}`,
            title: menu.display_name,
            children: [],
        });
    });

    permissions.forEach(permission => {
        if (permission.menu_id && menuMap.has(permission.menu_id)) {
            menuMap.get(permission.menu_id)!.children.push({
                key: permission.id,
                title: permission.display_name,
                children: [],
            });
        }
    });

    return Array.from(menuMap.values()).filter(node => node.children.length > 0);
};


const RoleManagementPage: React.FC = () => {
    const queryClient = useQueryClient();
    const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
    const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
    const [editingRole, setEditingRole] = useState<Role | null>(null);
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
    const [form] = Form.useForm();

    // --- Queries ---
    const { data: roles, isLoading: isLoadingRoles } = useQuery<Role[], Error>({ queryKey: ['roles'], queryFn: getRoles });
    const { data: permissions, isLoading: isLoadingPermissions } = useQuery<Permission[], Error>({ queryKey: ['permissions'], queryFn: getPermissions });
    const { data: menus, isLoading: isLoadingMenus } = useQuery({ queryKey: ['menus'], queryFn: async () => [] as Menu[] }); // Placeholder, assuming menus are fetched elsewhere or static

    const permissionTree = useMemo(() => buildPermissionTree(permissions || [], menus || []), [permissions, menus]);

    // --- Mutations ---
    const createRoleMutation = useMutation({ mutationFn: createRole, onSuccess: () => { message.success('Role created'); queryClient.invalidateQueries({ queryKey: ['roles'] }); setIsRoleModalOpen(false); } });
    const updateRoleMutation = useMutation({ mutationFn: (data: RoleUpdate & { id: number }) => updateRole(data.id, data), onSuccess: () => { message.success('Role updated'); queryClient.invalidateQueries({ queryKey: ['roles'] }); setIsRoleModalOpen(false); } });
    const deleteRoleMutation = useMutation({ mutationFn: deleteRole, onSuccess: () => { message.success('Role deleted'); queryClient.invalidateQueries({ queryKey: ['roles'] }); } });
    const assignPermissionsMutation = useMutation({
        mutationFn: (data: { roleId: number, permissionIds: number[] }) => assignPermissionsToRole(data.roleId, data.permissionIds),
        onSuccess: () => {
            message.success('Permissions updated');
            queryClient.invalidateQueries({ queryKey: ['roles'] });
            setIsPermissionModalOpen(false);
        }
    });

    // --- Modal and Form Logic ---
    const showRoleModal = (role: Role | null = null) => {
        setEditingRole(role);
        form.setFieldsValue(role || { name: '', display_name: '' });
        setIsRoleModalOpen(true);
    };
    
    const showPermissionModal = (role: Role) => {
        setEditingRole(role);
        setCheckedKeys(role.permissions.map(p => p.id));
        setIsPermissionModalOpen(true);
    };

    const handleRoleFormFinish = (values: RoleCreate) => {
        if (editingRole) {
            updateRoleMutation.mutate({ ...values, id: editingRole.id });
        } else {
            createRoleMutation.mutate(values);
        }
    };
    
    const handlePermissionFormFinish = () => {
        if (editingRole) {
            const numericKeys = checkedKeys.filter(k => typeof k === 'number') as number[];
            assignPermissionsMutation.mutate({ roleId: editingRole.id, permissionIds: numericKeys });
        }
    };

    // --- Table Columns ---
    const columns: ColumnsType<Role> = [
        { title: 'ID', dataIndex: 'id', key: 'id' },
        { title: 'Name (Identifier)', dataIndex: 'name', key: 'name' },
        { title: 'Display Name', dataIndex: 'display_name', key: 'display_name' },
        { title: 'Permissions', dataIndex: 'permissions', key: 'permissions', render: (perms: Permission[]) => `${perms.length} permissions` },
        {
            title: 'Actions',
            key: 'actions',
            render: (_: any, record: Role) => (
                <Space>
                    <Button icon={<KeyOutlined />} onClick={() => showPermissionModal(record)}>Permissions</Button>
                    <Button icon={<EditOutlined />} onClick={() => showRoleModal(record)} />
                    <Popconfirm title="Delete this role?" onConfirm={() => deleteRoleMutation.mutate(record.id)}>
                        <Button icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    return (
        <Spin spinning={isLoadingRoles || isLoadingPermissions || isLoadingMenus}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => showRoleModal()} style={{ marginBottom: 16 }}>Create Role</Button>
            <Table columns={columns} dataSource={roles} rowKey="id" />
            
            <Modal title={editingRole ? 'Edit Role' : 'Create Role'} open={isRoleModalOpen} onCancel={() => setIsRoleModalOpen(false)} onOk={form.submit} confirmLoading={createRoleMutation.isPending || updateRoleMutation.isPending}>
                <Form form={form} layout="vertical" onFinish={handleRoleFormFinish}>
                    <Form.Item name="name" label="Name (Identifier)" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="display_name" label="Display Name" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                </Form>
            </Modal>
            
            <Modal title={`Permissions for ${editingRole?.display_name}`} open={isPermissionModalOpen} onCancel={() => setIsPermissionModalOpen(false)} onOk={handlePermissionFormFinish} confirmLoading={assignPermissionsMutation.isPending} width={600}>
                {permissionTree.length > 0 ? (
                    <Tree
                        checkable
                        onCheck={(keys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
                            if (Array.isArray(keys)) {
                                setCheckedKeys(keys);
                            } else {
                                setCheckedKeys(keys.checked);
                            }
                        }}
                        checkedKeys={checkedKeys}
                        treeData={permissionTree}
                        defaultExpandAll
                    />
                ) : <p>No permissions found.</p>}
            </Modal>
        </Spin>
    );
};

export default RoleManagementPage; 