import { test, expect } from '@playwright/test';

test.describe('User Management Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 登录管理员账户
    await page.goto('/login');
    await page.fill('input[placeholder*="用户名"], input[placeholder*="Username"]', 'admin');
    await page.fill('input[type="password"]', 'adminpassword');
    await page.click('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    
    // 等待重定向到用户管理页面
    await page.waitForURL('**/users');
  });

  test('should display user management page', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h1, .ant-typography-title')).toContainText(['用户管理', 'User Management']);
    
    // 检查用户表格是否存在
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 检查添加用户按钮是否存在
    await expect(page.locator('button:has-text("添加"), button:has-text("Add"), button:has(.anticon-plus)')).toBeVisible();
  });

  test('should open create user modal', async ({ page }) => {
    // 点击添加用户按钮
    await page.click('button:has-text("添加"), button:has-text("Add"), button:has(.anticon-plus)');
    
    // 检查模态框是否打开
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('.ant-modal-title')).toContainText(['添加用户', 'Add User', '创建用户', 'Create User']);
    
    // 检查表单字段
    await expect(page.locator('input[placeholder*="用户名"], input[id*="username"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="邮箱"], input[type="email"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="密码"], input[type="password"]')).toBeVisible();
  });

  test('should create a new user', async ({ page }) => {
    // 点击添加用户按钮
    await page.click('button:has-text("添加"), button:has-text("Add"), button:has(.anticon-plus)');
    
    // 等待模态框出现
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 填写用户信息
    const timestamp = Date.now();
    await page.fill('input[placeholder*="用户名"], input[id*="username"]', `testuser${timestamp}`);
    await page.fill('input[placeholder*="邮箱"], input[type="email"]', `testuser${timestamp}@example.com`);
    await page.fill('input[placeholder*="密码"], input[type="password"]', 'testpassword123');
    await page.fill('input[placeholder*="姓名"], input[id*="fullName"], input[id*="full_name"]', 'Test User');
    
    // 提交表单
    await page.click('.ant-modal button[type="submit"], .ant-modal button:has-text("确定"), .ant-modal button:has-text("OK")');
    
    // 等待成功消息
    await expect(page.locator('.ant-message-success')).toBeVisible();
    
    // 检查新用户是否出现在表格中
    await expect(page.locator('.ant-table-tbody')).toContainText(`testuser${timestamp}`);
  });

  test('should edit user information', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.ant-table-tbody tr');
    
    // 点击第一个编辑按钮
    await page.click('.ant-table-tbody tr:first-child button:has(.anticon-edit), .ant-table-tbody tr:first-child button:has-text("编辑")');
    
    // 等待编辑模态框出现
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('.ant-modal-title')).toContainText(['编辑用户', 'Edit User']);
    
    // 修改用户信息
    await page.fill('input[placeholder*="姓名"], input[id*="fullName"], input[id*="full_name"]', 'Updated User Name');
    
    // 提交更改
    await page.click('.ant-modal button[type="submit"], .ant-modal button:has-text("确定"), .ant-modal button:has-text("OK")');
    
    // 等待成功消息
    await expect(page.locator('.ant-message-success')).toBeVisible();
  });

  test('should search users', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.ant-table-tbody tr');
    
    // 查找搜索输入框
    const searchInput = page.locator('input[placeholder*="搜索"], input[placeholder*="Search"]');
    if (await searchInput.isVisible()) {
      // 输入搜索关键词
      await searchInput.fill('admin');
      
      // 等待搜索结果
      await page.waitForTimeout(1000);
      
      // 检查搜索结果
      await expect(page.locator('.ant-table-tbody')).toContainText('admin');
    }
  });
});
