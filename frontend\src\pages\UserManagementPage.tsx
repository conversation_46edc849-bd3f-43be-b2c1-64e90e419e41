import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Table, Button, Modal, Form, Input, Switch, Select, message, Popconfirm, Tag, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { getUsers, createUser, updateUser, deleteUser, getRoles } from '../services/api';
import type { User, Role } from '../types/entities';
import type { UserCreate, UserUpdate } from '../services/types';
import Permission from '../components/Permission';

const UserManagementPage: React.FC = () => {
    const queryClient = useQueryClient();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingUser, setEditingUser] = useState<User | null>(null);
    const [form] = Form.useForm();

    // Fetch users
    const { data: users, isLoading: isLoadingUsers } = useQuery<User[], Error>({
        queryKey: ['users'],
        queryFn: getUsers,
    });

    // Fetch roles for the Select dropdown
    const { data: roles, isLoading: isLoadingRoles } = useQuery<Role[], Error>({
        queryKey: ['roles'],
        queryFn: getRoles,
    });

    const roleOptions = roles?.map(role => ({ label: role.display_name, value: role.id }));

    // --- Mutations ---
    const createUserMutation = useMutation({
        mutationFn: createUser,
        onSuccess: () => {
            message.success('User created successfully');
            queryClient.invalidateQueries({ queryKey: ['users'] });
            setIsModalOpen(false);
        },
        onError: (error: Error) => message.error(error.message),
    });

    const updateUserMutation = useMutation({
        mutationFn: (data: UserUpdate & { id: number }) => updateUser(data.id, data),
        onSuccess: () => {
            message.success('User updated successfully');
            queryClient.invalidateQueries({ queryKey: ['users'] });
            setIsModalOpen(false);
        },
        onError: (error: Error) => message.error(error.message),
    });

    const deleteUserMutation = useMutation({
        mutationFn: deleteUser,
        onSuccess: () => {
            message.success('User deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['users'] });
        },
        onError: (error: Error) => message.error(error.message),
    });

    // --- Modal and Form Logic ---
    const showModal = (user: User | null = null) => {
        setEditingUser(user);
        if (user) {
            form.setFieldsValue({
                ...user,
                role_ids: user.roles.map(r => r.id),
                password: '', // Clear password on edit
            });
        } else {
            form.resetFields();
            form.setFieldsValue({ is_active: true });
        }
        setIsModalOpen(true);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const onFinish = (values: UserCreate | UserUpdate) => {
        if (editingUser) {
            updateUserMutation.mutate({ ...values, id: editingUser.id });
        } else {
            createUserMutation.mutate(values as UserCreate);
        }
    };

    // --- Table Columns ---
    const columns: ColumnsType<User> = [
        { title: 'ID', dataIndex: 'id', key: 'id', sorter: (a: User, b: User) => a.id - b.id },
        { title: 'Username', dataIndex: 'username', key: 'username', sorter: (a: User, b: User) => a.username.localeCompare(b.username) },
        {
            title: 'Roles',
            dataIndex: 'roles',
            key: 'roles',
            render: (roles: Role[]) => (
                <>
                    {roles.map((role: Role) => <Tag key={role.id}>{role.display_name}</Tag>)}
                </>
            ),
        },
        {
            title: 'Active',
            dataIndex: 'is_active',
            key: 'is_active',
            render: (isActive: boolean) => <Tag color={isActive ? 'success' : 'error'}>{isActive ? 'Yes' : 'No'}</Tag>,
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (_: any, record: User) => (
                <Space>
                    <Button icon={<EditOutlined />} onClick={() => showModal(record)} />
                    <Popconfirm
                        title="Delete this user?"
                        description="This action cannot be undone."
                        onConfirm={() => deleteUserMutation.mutate(record.id)}
                        okText="Yes"
                        cancelText="No"
                    >
                        <Button icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    return (
        <div>
            <Permission required="user_management:create">
                <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => showModal()}
                    style={{ marginBottom: 16 }}
                >
                    Create User
                </Button>
            </Permission>
            <Table
                columns={columns}
                dataSource={users}
                loading={isLoadingUsers || isLoadingRoles}
                rowKey="id"
            />
            <Modal
                title={editingUser ? 'Edit User' : 'Create User'}
                open={isModalOpen}
                onCancel={handleCancel}
                onOk={form.submit}
                confirmLoading={createUserMutation.isPending || updateUserMutation.isPending}
            >
                <Form form={form} layout="vertical" name="user_form" onFinish={onFinish}>
                    <Form.Item
                        name="username"
                        label="Username"
                        rules={[{ required: true, message: 'Please input the username!' }]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="password"
                        label="Password"
                        rules={[{ required: !editingUser, message: 'Please input the password!' }]}
                    >
                        <Input.Password placeholder={editingUser ? "Leave blank to keep current password" : ""} />
                    </Form.Item>
                    <Form.Item name="role_ids" label="Roles">
                        <Select
                            mode="multiple"
                            allowClear
                            style={{ width: '100%' }}
                            placeholder="Please select roles"
                            options={roleOptions}
                            loading={isLoadingRoles}
                        />
                    </Form.Item>
                    <Form.Item name="is_active" label="Is Active" valuePropName="checked">
                        <Switch />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default UserManagementPage; 