import os
from dotenv import load_dotenv

# --- Environment Variable Loading ---
# Build the absolute path to the .env file located in the 'backend' directory
# and load it. This must happen before any other project modules are imported.
dotenv_path = os.path.join(os.path.dirname(__file__), 'backend', '.env')
load_dotenv(dotenv_path=dotenv_path)

DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    print(f"FATAL: DATABASE_URL not found. Attempted to load from path: {dotenv_path}")
    print("Please ensure a .env file exists in the 'backend' directory with the DATABASE_URL.")
    exit(1)

# --- Main Application Imports ---
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from backend.database import engine, SessionLocal, Base
from backend.routers import auth, user, role, menu
from backend import init_db

# --- FastAPI App Initialization ---
app = FastAPI()

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Allows the frontend dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(user.router)
app.include_router(role.router)
app.include_router(menu.router)

@app.on_event("startup")
def startup_event():
    print("Running startup event: Initializing DB...")
    # Using the "nuclear option" for development to ensure clean state
    from sqlalchemy import text
    with engine.connect() as connection:
        connection.execution_options(isolation_level="AUTOCOMMIT").execute(text("DROP SCHEMA public CASCADE;"))
        connection.execute(text("CREATE SCHEMA public;"))
    
    # Now create all tables on the fresh schema
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    init_db.init_db(db)
    db.close()
    print("Startup event finished.")

@app.get("/")
def read_root():
    return {"message": "Welcome to the API"}

if __name__ == "__main__":
    uvicorn.run("run:app", host="127.0.0.1", port=8000, reload=True) 