# 前端主布局 UI/UX 设计需求文档

## 1. 设计目标与原则

-   **目标**: 设计一个清晰、一致、高效且响应迅速的主工作区布局，为用户提供无缝的操作体验。
-   **设计原则**:
    -   **信息架构清晰**: 采用经典的“顶部主导航 + 左侧子导航”的二级导航模式，确保用户能快速定位功能。
    -   **状态驱动UI**: 布局中的所有动态元素（如菜单、用户信息）都必须由 `AuthContext` 中的状态驱动，不得有任何硬编码。
    -   **响应式与一致性**: 布局应在主流浏览器和分辨率下保持一致性和可用性。
    -   **提供即时反馈**: 对用户的操作（如点击菜单）和系统的状态（如加载中）提供及时的视觉反馈。

---

## 2. 布局结构 (Layout Structure)

主布局由 Ant Design 的 `Layout` 组件构建，采用经典的“上-左-右”结构。

-   **`<Layout>` (最外层容器)**:
    -   **方向**: 垂直布局 (`flexDirection: 'column'`)。
    -   **高度**: 占满整个视口高度 (`height: '100vh'`)，防止页面内容溢出时出现两层滚动条。

-   **包含的三个主要部分**:
    1.  **顶部全局导航栏 (`<Header>`)**: 固定在页面顶部，高度固定，不随内容滚动。
    2.  **二级内容区 (`<Layout>`)**: 位于Header下方，占据剩余的所有垂直空间，水平排列。
        -   **左侧子菜单栏 (`<Sider>`)**: 位于二级内容区左侧。
        -   **右侧主内容区 (`<Content>`)**: 位于二级内容区右侧，是所有页面组件的渲染区域。

---

## 3. 组件详细设计规约

### 3.1 顶部全局导航栏 (`<Header>`)

-   **背景**: 白色，底部有一条浅灰色边框线 (`#f0f0f0`)，以与内容区分隔。
-   **布局**: 采用 `Flex` 布局，从左到右依次为：Logo与系统名称、一级菜单区域、用户操作区域。

#### 3.1.1 Logo 与系统名称
-   **组成**: 一个图标 (`WifiOutlined`) + 系统名称文本 ("HuqAI")。
-   **交互**: 整个区域是一个可点击的链接，点击后导航至系统首页 (`/dashboard`)。

#### 3.1.2 一级菜单区域 (`<Menu mode="horizontal">`)
-   **数据源**: 由 `AuthContext` 提供的用户专属菜单树的**第一层**动态生成。如果用户无权访问某个一级菜单，该菜单项将不会被渲染。
-   **视觉**:
    -   **高亮**: 当前所在路由对应的一级菜单项，应有高亮显示（`selectedKeys`）。
    -   **例如**: 当用户访问 `/system/user-management` 时，“系统管理”这个一级菜单应处于高亮状态。
-   **交互**: 点击不同的一级菜单项时：
    1.  应立即更新左侧的子菜单栏，显示所点击一级菜单下的二级菜单。
    2.  如果该一级菜单下有默认的子页面，应自动导航至该子页面的路径。

#### 3.1.3 用户操作区域
-   **组成**: 从左到右为：通知图标、用户头像和用户名。
-   **通知图标 (`<Badge>` + `<BellOutlined>`)**:
    -   静态展示，右上角用 `Badge` 显示未读消息数（当前为静态数字`5`）。
-   **用户信息 (`<Dropdown>`)**:
    -   **显示**: 由用户头像 (`<Avatar>`) 和用户名 (`user.username`) 组成。
    -   **交互**: 点击此区域会弹出一个下拉菜单 (`Dropdown`)。
    -   **下拉菜单项**:
        -   个人中心
        -   设置
        -   分割线
        -   **退出登录**: 点击后，执行 `AuthContext` 中的 `logout` 方法，清除本地认证信息，并强制导航至 `/login` 页面。

### 3.2 左侧子菜单栏 (`<Sider>`)

-   **可见性**: **当且仅当**当前选中的一级菜单下**存在有权访问的二级菜单**时，此侧边栏才会被渲染。如果一个一级菜单是光杆司令（没有子菜单），则左侧栏不显示。
-   **视觉**:
    -   **主题**: 浅色主题。
    -   **宽度**: 默认宽度 `200px`。
-   **交互**:
    -   **折叠/展开**:
        -   在右侧内容区的 Header 左上角，提供一个折叠/展开按钮 (`MenuFoldOutlined` / `MenuUnfoldOutlined`)。
        -   点击该按钮可以切换 Sider 的折叠状态 (`collapsed`)。折叠后，Sider 宽度变窄，只显示图标。
    -   **菜单内容 (`<Menu mode="inline">`)**:
        -   **数据源**: 根据顶部选中的一级菜单，动态获取其 `children` 数组，并渲染成多级内联菜单。
        -   **高亮与展开**:
            -   当前页面的菜单项应高亮显示 (`selectedKeys`)。
            -   当前页面所在的父级菜单应自动展开 (`openKeys`)。
            -   **例如**: 访问 `/system/user-management` 时，`Sider` 中“系统管理”这个可展开的菜单项应自动展开，并且其下的“用户管理”子菜单项应高亮。
        -   **图标**: 每个菜单项前都应显示其在数据中定义的 `icon`。若无定义，则显示一个默认图标。

### 3.3 右侧主内容区 (`<Content>`)

-   **职责**: 作为所有功能页面的容器，通过 `<Outlet />` 组件渲染当前路由匹配的页面。
-   **视觉**:
    -   应有统一的内边距 (`padding`)，确保页面内容不会紧贴边框。
    -   背景色为浅灰色 (`#f0f2f5` 或类似)，与白色卡片式的内容形成对比。
-   **交互**:
    -   **滚动条**: 当内容超出视口高度时，**只有**内容区自身出现滚动条，整体布局保持固定。
    -   **面包屑导航 (`<Breadcrumb>`)**:
        -   **位置**: 固定在内容区的最上方。
        -   **数据源**: 根据当前路由 `location.pathname` 和完整的菜单树数据，动态计算生成。
        -   **逻辑**: 从菜单树的根节点开始，递归查找匹配当前路径的节点链，并将路径上的所有节点名称展示出来。
        -   **例如**: 访问 `/system/user-management` 时，面包屑应显示为“首页 / 系统管理 / 用户管理”。最后一级为纯文本，前面的级别为可点击的 `Link`。

### 3.4 全局加载状态

-   **触发时机**: 当 `AuthContext` 中的 `loading` 状态为 `true` 时（通常发生在应用首次加载、或登录后正在获取用户/菜单数据的过程中）。
-   **表现**: 整个页面被一个半透明的遮罩层覆盖，中央显示一个大的加载指示器 (`<Spin size="large">`) 和提示文字“加载中...”，以防止用户在数据未准备好时进行操作。