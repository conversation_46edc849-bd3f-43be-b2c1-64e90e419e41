# 项目开发计划文档 (V2.1 - 进度百分比版)

## 1. 文档目的
本计划旨在根据项目 `README.md` 的内容，为"企业级前后端分离应用基础框架"项目提供一个清晰、分阶段、可跟踪的开发路线图。它将项目分解为更具体的子任务，并引入状态管理，以指导开发团队高效、协同地完成项目，并实时记录开发进度。

## 2. 开发阶段与任务分解
我们将项目开发分为五个主要阶段。每个任务都包含详细步骤，并明确了依赖关系和当前状态。

**状态说明:**
使用百分比记录任务进度。
-   `0.00%`: 代表任务**未开始**。
-   `100.00%`: 代表任务**已完成**。
-   `(0.00%, 100.00%)`: 代表任务**进行中**。
-   `已阻塞 (Blocked)`: 状态可用于标记因外部原因暂停的任务。

---

### **阶段一：项目初始化与基础建设 (预计 3 天)**
**目标**: 搭建前后端项目的骨架，配置开发工具和代码规范，为后续编码工作奠定坚实基础。

| 任务 ID | 主要任务及详细步骤 | 负责人角色 | 依赖项 | 状态 | 产出物 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **P1-T1** | **后端**: 初始化 FastAPI 项目，配置代码规范工具。<br>- 1. 手动创建 `backend` 目录及项目结构。<br>- 2. `pip install fastapi uvicorn sqlalchemy pydantic python-dotenv psycopg2-binary`<br>- 3. `pip install black isort flake8`<br>- 4. 配置 `pyproject.toml` 或 `.flake8` 文件。 | Backend | - | 100.00% | 初始化的 FastAPI 项目 |
| **P1-T2** | **前端**: 初始化 Vite + React + TS 项目，配置代码规范工具。<br>- 1. `npm create vite@latest frontend -- --template react-ts`<br>- 2. `npm install eslint prettier`<br>- 3. 创建并配置 `.eslintrc.cjs` 和 `.prettierrc`。 | Frontend | - | 100.00% | 初始化的 Vite 项目 |
| **P1-T3** | 在前后端项目中配置 `husky` 和 `lint-staged`。<br>- 1. 在 `frontend` 目录 `npm install --save-dev husky lint-staged`<br>- 2. 配置 `package.json` 中的 `lint-staged` 规则。<br>- 3. 设置 `pre-commit` Git 钩子。 | Full-stack | P1-T1, P1-T2 | 100.00% | 提交前的代码质量保障机制 |
| **P1-T4** | **后端**: 定义核心 SQLAlchemy 模型。<br>- 1. 创建 `backend/models.py`。<br>- 2. 定义 `User`, `Role`, `Permission`, `Menu`, `role_permissions` 表结构，严格遵循 RBAC 设计文档。 | Backend | - | 100.00% | `backend/models.py` |
| **P1-T5** | **后端**: 实现数据库连接和会话管理模块。<br>- 1. 创建 `backend/database.py`。<br>- 2. 定义 `SQLALCHEMY_DATABASE_URL` 并从 `.env` 读取。<br>- 3. 创建 `engine`, `SessionLocal`, 和 `get_db` 依赖。 | Backend | P1-T4 | 100.00% | `backend/database.py` |

---

### **阶段二：核心认证流程实现 (预计 5 天)**
**目标**: 完成后端的用户认证和前端的登录、登出、路由守卫等基础流程，打通前后端认证的"任督二脉"。

| 任务 ID | 主要任务及详细步骤 | 负责人角色 | 依赖项 | 状态 | 产出物 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **P2-T1** | **后端**: 实现安全服务（密码哈希与JWT）。<br>- 1. 安装 `passlib[bcrypt]` 和 `python-jose`。<br>- 2. 创建 `security.py` 实现密码验证/哈希和JWT创建/解码函数。| Backend | - | 100.00% | 认证服务模块 |
| **P2-T2** | **后端**: 创建登录 API 端点。<br>- 1. 创建 `schemas/token.py` 等 Pydantic 模型。<br>- 2. 在 `routers/auth.py` 中实现 `/api/auth/token` 端点。<br>- 3. 创建 `get_current_user` 依赖项用于保护路由。 | Backend | P2-T1, P1-T4 | 100.00% | 登录 API |
| **P2-T3** | **前端**: 封装统一的 `Axios` 实例。<br>- 1. 创建 `frontend/src/services/api.ts`。<br>- 2. 实现请求拦截器，用于注入 Token。<br>- 3. 实现响应拦截器，用于处理全局业务错误和 HTTP 401 等状态。| Frontend | - | 100.00% | `api.ts` |
| **P2-T4** | **前端**: 创建登录页面并实现登录逻辑。<br>- 1. 安装 `zustand` `react-query` `react-router-dom` `antd`。<br>- 2. 创建 Zustand store 管理用户状态和 Token。<br>- 3. 使用 Ant Design 创建登录表单。<br>- 4. 使用 `react-query` 的 `useMutation` | Frontend | P2-T2, P2-T3 | 100.00% | 可用的登录/登出功能 |
| **P2-T5** | **前端**: 实现路由守卫和主布局。<br>- 1. 创建 `ProtectedRoute` 组件检查登录状态。<br>- 2. 在路由配置中应用保护，区分公私路由。<br>- 3. 使用 AntD `Layout` 组件创建主布局框架。| Frontend | P2-T4 | 100.00% | 受保护的应用主框架 |
| **P2-T6** | **后端**: 编写初始数据填充脚本 (`init_db.py`)。<br>- 1. 创建默认管理员角色和用户。<br>- 2. 确保脚本的幂等性（重复运行不报错）。<br>- 3. 在 `run.py` 中调用此脚本。 | Backend | P1-T4 | 100.00% | `init_db.py` |

---

### **阶段三：后端核心业务 API 开发 (预计 7 天)**
**目标**: 开发所有 RBAC 相关的管理接口，包括关键的权限-菜单联动逻辑。

| 任务 ID | 主要任务及详细步骤 | 负责人角色 | 依赖项 | 状态 | 产出物 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **P3-T1** | **后端**: 实现用户/角色/权限/菜单的 CRUD 服务。 | Backend | P1-T5 | 100.00% | `services` 层的业务逻辑 |
| **P3-T2** | **后端**: 实现用户/角色/权限/菜单的 CRUD API 端点，并使用权限依赖保护。 | Backend | P3-T1, P2-T2 | 100.00% | RBAC 相关的 API |
| **P3-T3** | **后端**: **核心**: 在菜单服务中，实现创建/更新菜单时自动生成关联权限的逻辑。 | Backend | P3-T1 | 100.00% | 自动化的权限生成逻辑 |
| **P3-T4** | **后端**: 实现为角色分配权限的接口 (`/api/roles/{id}/permissions`)。 | Backend | P3-T1 | 100.00% | 角色权限分配 API |
| **P3-T5** | **后端**: 实现获取当前用户菜单和权限的接口 (`/api/menus/me`)。| Backend | P3-T1 | 100.00% | 动态菜单 API |

---

### **阶段四：前端核心功能页面实现 (预计 9 天)**
**目标**: 对接后端 API，完成所有核心管理页面的开发，实现一个功能完整的后台管理界面。

| 任务 ID | 主要任务及详细步骤 | 负责人角色 | 依赖项 | 状态 | 产出物 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **P4-T1** | **前端**: 实现动态导航与面包屑。<br>- 1. 调用 `/api/menus/me` 获取数据。<br>- 2. 递归渲染顶部和左侧菜单。<br>- 3. 根据路由动态生成面包屑。 | Frontend | P3-T5 | 100.00% | 动态导航功能 |
| **P4-T2** | **前端**: 创建用户管理页面。<br>- 1. 使用 `useQuery` 获取用户列表。<br>- 2. 使用 AntD `Table` 展示数据，并实现分页、搜索。<br>- 3. 使用 `useMutation` 实现增、删、改功能。 | Frontend | P3-T2 | 100.00% | 用户管理页面 |
| **P4-T3** | **前端**: 创建角色管理页面，并实现权限分配功能。<br>- 1. 页面结构同用户管理。<br>- 2. 在模态框中使用 AntD `Tree` 组件实现权限的勾选和提交。| Frontend | P3-T2, P3-T4 | 100.00% | 角色管理页面 |
| **P4-T4** | **前端**: 创建菜单管理页面。<br>- 1. 使用 AntD `Table` 的树状展示功能。<br>- 2. 实现对菜单的增、删、改。 | Frontend | P3-T2 | 100.00% | 菜单管理页面 |
| **P4-T5** | **前端**: 全局实现基于权限的 UI 控制。<br>- 1. 创建一个 `usePermissions` hook 返回权限列表。<br>- 2. 创建一个 `Permission` 组件，根据权限判断是否渲染子组件（如按钮）。 | Frontend | P3-T5 | 100.00% | 精细化的 UI 权限控制 |

---

### **阶段五：测试、优化与文档完善 (预计 4 天)**
**目标**: 保证项目质量，修复 Bug，优化性能，并确保文档与最终代码一致。

| 任务 ID | 主要任务及详细步骤 | 负责人角色 | 依赖项 | 状态 | 产出物 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **P5-T1** | **后端**: 编写单元/集成测试。<br>- 1. 为 `security`、`services` 核心逻辑编写单元测试。<br>- 2. 为核心 API 端点编写集成测试。 | Backend | P3 | 0.00% | 后端测试用例 |
| **P5-T2** | **前端**: 编写单元测试。<br>- 1. 为 `usePermissions` hook 等关键逻辑编写测试。<br>- 2. 为 `Permission` 等核心组件编写测试。 | Frontend | P4 | 0.00% | 前端测试用例 |
| **P5-T3** | 进行全面的 E2E（端到端）和交叉功能测试。 | Full-stack | P3, P4 | 0.00% | Bug 列表及修复记录 |
| **P5-T4** | 审查并更新所有项目 `.md` 文档。 | Team Lead | All | 0.00% | 最终版项目文档 |

## 3. 高阶项目排期概览
```mermaid
gantt
    title 企业级前后端分离应用基础框架开发排期
    dateFormat  YYYY-MM-DD
    axisFormat %m-%d
    section 阶段一：初始化与基础
    项目初始化与基础建设 :done, 2024-07-01, 3d
    
    section 阶段二：核心认证
    核心认证流程实现 :done, 2024-07-04, 5d

    section 阶段三：后端API
    后端核心业务 API 开发 :2024-07-11, 7d

    section 阶段四：前端页面
    前端核心功能页面实现 :2024-07-20, 9d

    section 阶段五：测试与文档
    测试、优化与文档完善 :2024-08-01, 4d
```
*注意：以上排期为估算，实际时间可能因开发过程中的具体情况而调整。*

## 4. 开发进度记录
| 日期 | 阶段 | 任务 ID | 进度描述 | 责任人 |
| :--- | :--- | :--- | :--- | :--- |
| 2024-07-01 | 阶段一 | - | 项目启动，阶段一开发开始。 | Team Lead |
| | | | | |
| | | | | |
*（此表格用于记录关键节点的进度更新）*