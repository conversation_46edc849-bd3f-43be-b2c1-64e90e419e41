# 后端核心配置与启动说明

### 1. 应用启动入口 (`run.py`)

`run.py` 脚本是整个后端服务的核心启动入口。它的主要职责和意义如下：

1.  **应用实例化**: 创建并配置 FastAPI 的主应用实例 (`app = FastAPI()`)。
2.  **启动顺序编排**: 定义了服务启动时一系列关键操作的执行顺序，是整个初始化流程的“总指挥”。
3.  **中间件加载**: 在此文件中配置并加载了关键的中间件，尤其是用于处理跨域请求的 `CORSMiddleware`。
4.  **数据库初始化**: 按顺序触发数据库的两个核心初始化步骤：首先创建数据表结构，然后填充初始数据。
5.  **路由注册**: 导入所有定义在 `backend/routers/` 目录下的 API 路由，并将它们注册到 FastAPI 应用中，使 API 端点可以被外部访问。
6.  **服务启动**: 在脚本的最后，通过 `uvicorn.run()` 命令启动 ASGI 服务器（Uvicorn），将 FastAPI 应用部署为一个可通过 HTTP 访问的 Web 服务。

简而言之，`run.py` 不仅是运行后端服务的命令目标，更是将数据库、业务逻辑路由和 Web 服务框架粘合在一起的中央协调器。

### 2. 数据库配置

-   **文件位置:** `backend/database.py`
-   **关键参数:** `SQLALCHEMY_DATABASE_URL`
-   **描述:** 此变量定义了 SQLAlchemy 用于连接数据库的DSN (Data Source Name)。
-   **当前配置 (开发):**
    ```python
    SQLALCHEMY_DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/webbasedb2?client_encoding=utf8"
    ```
-   **生产环境建议:**
    -   **严禁**在代码中硬编码生产环境的数据库凭证。
    -   应使用环境变量进行配置。建议修改 `database.py` 以从 `.env` 文件或系统环境变量中读取 `DATABASE_URL`。

    **修改示例 (`backend/database.py`):**
    ```python
    import os
    from dotenv import load_dotenv

    load_dotenv() # 从 .env 文件加载环境变量

    # 优先从环境变量获取，若不存在则使用默认的开发库地址
    SQLALCHEMY_DATABASE_URL = os.getenv(
        "DATABASE_URL",
        "postgresql://postgres:postgres@localhost:5432/webbasedb2?client_encoding=utf8"
    )
    ```

### 3. 跨域资源共享 (CORS) 配置

-   **文件位置:** `run.py`
-   **描述:** CORS 中间件用于允许来自指定源（通常是前端开发服务器）的跨域请求。
-   **当前配置 (开发):**
    ```python
    from fastapi.middleware.cors import CORSMiddleware

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:5173"], # 只允许 Vite dev server
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    ```
-   **生产环境建议:**
    -   `allow_origins` 列表应更新为生产环境前端应用的准确域名，例如 `["https://your-app-domain.com"]`。
    -   为安全起见，应避免在生产环境中使用 `["*"]`。

### 4. 数据库初始化

-   **触发位置:** `run.py`
-   **描述:** 在应用启动时，会自动执行数据库初始化流程，该流程包含两个主要步骤：

    1.  **创建数据表**:
        -   **代码:** `Base.metadata.create_all(bind=engine)`
        -   **作用:** 此命令会检查当前数据库，并根据 `backend/models.py` 文件中定义的 SQLAlchemy 模型，自动创建所有不存在的数据表。这是一个幂等操作，如果表已存在则不会进行任何更改。

    2.  **填充初始数据**:
        -   **代码:** `init_db()`
        -   **作用:** 调用位于 `backend/init_db.py` 的 `init_db` 函数，向数据库中填充系统运行所必需的初始数据。这包括默认的菜单结构、基于菜单生成的权限、预设的角色（如“超级管理员”）及其权限分配，以及一个默认的管理员账户。此脚本在设计上也是幂等的，会先检查数据是否存在，避免重复插入。