{"name": "rbac-system-e2e-tests", "version": "1.0.0", "description": "End-to-end tests for RBAC system", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:auth": "playwright test tests/auth.spec.ts", "test:api": "playwright test tests/api.spec.ts", "test:user-management": "playwright test tests/user-management.spec.ts"}, "devDependencies": {"@playwright/test": "^1.53.1", "playwright": "^1.53.1"}}