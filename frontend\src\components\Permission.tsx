import React from 'react';
import { usePermissions } from '../hooks/usePermissions';

interface PermissionProps {
    required: string;
    children: React.ReactNode;
}

const Permission: React.FC<PermissionProps> = ({ required, children }) => {
    const { hasPermission } = usePermissions();

    if (hasPermission(required)) {
        return <>{children}</>;
    }

    return null;
};

export default Permission; 