import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Table, Button, Modal, Form, Input, Select, message, Popconfirm, Space, InputNumber } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { getMenus, createMenu, updateMenu, deleteMenu } from '../services/api';
import type { Menu } from '../types/entities';
import type { MenuCreate, MenuUpdate } from '../services/types';

const { Option } = Select;

// Helper to build a tree from a flat list
const buildTree = (list: Menu[]): Menu[] => {
    const map = new Map<number, Menu & { children: Menu[] }>();
    const roots: (Menu & { children: Menu[] })[] = [];

    list.forEach(item => {
        map.set(item.id, { ...item, children: [] });
    });

    list.forEach(item => {
        if (item.parent_id && map.has(item.parent_id)) {
            map.get(item.parent_id)!.children.push(map.get(item.id)!);
        } else {
            roots.push(map.get(item.id)!);
        }
    });

    return roots;
};

const MenuManagementPage: React.FC = () => {
    const queryClient = useQueryClient();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [editingMenu, setEditingMenu] = useState<Menu | null>(null);
    const [form] = Form.useForm();

    const { data: menus, isLoading } = useQuery<Menu[], Error>({ queryKey: ['menus'], queryFn: getMenus });

    const menuTree = useMemo(() => buildTree(menus || []), [menus]);
    const menuOptions = useMemo(() => menus?.map(m => ({ label: m.display_name, value: m.id })), [menus]);

    const createMutation = useMutation({ mutationFn: createMenu, onSuccess: () => { message.success('Menu created'); queryClient.invalidateQueries({ queryKey: ['menus'] }); setIsModalOpen(false); } });
    const updateMutation = useMutation({ mutationFn: (data: { id: number; menu: MenuUpdate }) => updateMenu(data.id, data.menu), onSuccess: () => { message.success('Menu updated'); queryClient.invalidateQueries({ queryKey: ['menus'] }); setIsModalOpen(false); } });
    const deleteMutation = useMutation({ mutationFn: deleteMenu, onSuccess: () => { message.success('Menu deleted'); queryClient.invalidateQueries({ queryKey: ['menus'] }); } });

    const showModal = (menu: Menu | null = null) => {
        setEditingMenu(menu);
        form.setFieldsValue(menu || { key: '', display_name: '', path: '', order: 0, parent_id: null, icon: '' });
        setIsModalOpen(true);
    };

    const handleFinish = (values: MenuCreate) => {
        if (editingMenu) {
            updateMutation.mutate({ id: editingMenu.id, menu: values });
        } else {
            createMutation.mutate(values);
        }
    };

    const columns: ColumnsType<Menu> = [
        { title: 'Display Name', dataIndex: 'display_name', key: 'display_name' },
        { title: 'Path', dataIndex: 'path', key: 'path' },
        { title: 'Key', dataIndex: 'key', key: 'key' },
        { title: 'Order', dataIndex: 'order', key: 'order', sorter: (a: Menu, b: Menu) => a.order - b.order },
        {
            title: 'Actions',
            key: 'actions',
            render: (_: any, record: Menu) => (
                <Space>
                    <Button icon={<EditOutlined />} onClick={() => showModal(record)} />
                    <Popconfirm title="Delete this menu?" onConfirm={() => deleteMutation.mutate(record.id)}>
                        <Button icon={<DeleteOutlined />} danger />
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    return (
        <div>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()} style={{ marginBottom: 16 }}>Create Menu</Button>
            <Table
                columns={columns}
                dataSource={menuTree}
                loading={isLoading}
                rowKey="id"
                pagination={false}
            />
            <Modal title={editingMenu ? 'Edit Menu' : 'Create Menu'} open={isModalOpen} onCancel={() => setIsModalOpen(false)} onOk={form.submit} confirmLoading={createMutation.isPending || updateMutation.isPending}>
                <Form form={form} layout="vertical" onFinish={handleFinish}>
                    <Form.Item name="display_name" label="Display Name" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="key" label="Key (Unique Identifier)" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="path" label="Path (URL)" rules={[{ required: true }]}>
                        <Input />
                    </Form.Item>
                    <Form.Item name="parent_id" label="Parent Menu">
                        <Select allowClear options={menuOptions} />
                    </Form.Item>
                    <Form.Item name="order" label="Order" rules={[{ required: true, type: 'number' }]}>
                        <InputNumber style={{ width: '100%' }}/>
                    </Form.Item>
                    <Form.Item name="icon" label="Icon">
                        <Input />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default MenuManagementPage; 