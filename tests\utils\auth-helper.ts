import { Page } from '@playwright/test';

export class AuthHelper {
  constructor(private page: Page) {}

  async login(username: string = 'admin', password: string = 'adminpassword') {
    await this.page.goto('/login');
    await this.page.fill('input[placeholder*="用户名"], input[placeholder*="Username"]', username);
    await this.page.fill('input[type="password"]', password);
    await this.page.click('button[type="submit"], button:has-text("登录"), button:has-text("Login")');
    
    // 等待重定向
    await this.page.waitForURL('**/users');
  }

  async logout() {
    await this.page.click('button:has-text("登出"), button:has-text("Logout"), .ant-btn:has(.anticon-logout)');
    await this.page.waitForURL('**/login');
  }

  async isLoggedIn(): Promise<boolean> {
    try {
      await this.page.waitForSelector('button:has-text("登出"), button:has-text("Logout")', { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}

export class ApiHelper {
  private baseURL = 'http://localhost:8000';
  private token: string | null = null;

  async authenticate(username: string = 'admin', password: string = 'adminpassword') {
    const response = await fetch(`${this.baseURL}/api/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `username=${username}&password=${password}`,
    });

    if (response.ok) {
      const data = await response.json();
      this.token = data.access_token;
      return this.token;
    }
    throw new Error('Authentication failed');
  }

  async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return fetch(url, {
      ...options,
      headers,
    });
  }

  async get(endpoint: string) {
    return this.request(endpoint, { method: 'GET' });
  }

  async post(endpoint: string, data: any) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put(endpoint: string, data: any) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete(endpoint: string) {
    return this.request(endpoint, { method: 'DELETE' });
  }
}
