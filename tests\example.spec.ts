import { test, expect } from '@playwright/test';

test.describe('RBAC系统基础测试', () => {
  test('应该能够访问登录页面', async ({ page }) => {
    await page.goto('/login');
    
    // 检查页面标题
    await expect(page).toHaveTitle(/登录|Login/);
    
    // 检查登录表单元素
    await expect(page.locator('input[type="text"], input[placeholder*="用户名"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('应该能够使用管理员账户登录', async ({ page }) => {
    await page.goto('/login');
    
    // 填写登录信息
    await page.fill('input[type="text"], input[placeholder*="用户名"]', 'admin');
    await page.fill('input[type="password"]', 'adminpassword');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 等待重定向到主页面
    await page.waitForURL('**/users');
    
    // 验证登录成功
    await expect(page.locator('h1, .ant-typography-title')).toContainText(['用户管理', 'User Management']);
  });
});
