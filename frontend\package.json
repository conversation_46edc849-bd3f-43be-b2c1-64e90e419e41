{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.3.6", "@tanstack/react-query": "^5.29.2", "antd": "5.15.2", "axios": "^1.6.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}