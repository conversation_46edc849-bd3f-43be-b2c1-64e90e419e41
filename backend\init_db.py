from sqlalchemy.orm import Session
from . import models, schemas
from .services import user_service, menu_service

def init_db(db: Session):
    # 使用一个事务来初始化所有数据
    
    # 1. 创建角色
    admin_role = db.query(models.Role).filter(models.Role.name == "Admin").first()
    if not admin_role:
        admin_role = models.Role(name="Admin", display_name="超级管理员")
        db.add(admin_role)

    user_role = db.query(models.Role).filter(models.Role.name == "User").first()
    if not user_role:
        user_role = models.Role(name="User", display_name="普通用户")
        db.add(user_role)
        
    # Flush a session to get the IDs for roles before creating the user.
    db.flush()

    # 2. 创建用户
    admin_user = db.query(models.User).filter(models.User.email == "<EMAIL>").first()
    if not admin_user:
        user_in = schemas.UserCreate(
            email="<EMAIL>",
            password="adminpassword",
            full_name="Admin User",
            role_ids=[admin_role.id]
        )
        user_service.create_user(db=db, user=user_in)

    # 3. 创建菜单
    if not db.query(models.Menu).first():
        menu_service.create_menu(db, schemas.MenuCreate(key="dashboard", display_name="Dashboard", path="/", order=0))
        menu_service.create_menu(db, schemas.MenuCreate(key="user_management", display_name="User Management", path="/users", order=1))
        menu_service.create_menu(db, schemas.MenuCreate(key="role_management", display_name="Role Management", path="/roles", order=2))
        menu_service.create_menu(db, schemas.MenuCreate(key="menu_management", display_name="Menu Management", path="/menus", order=3))

    db.commit() 