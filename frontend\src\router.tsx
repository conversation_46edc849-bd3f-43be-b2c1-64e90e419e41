import React from 'react';
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import LoginPage from './pages/LoginPage';
import useAuthStore from './store/authStore';
import MainLayout from './layouts/MainLayout';
import UserManagementPage from './pages/UserManagementPage';
import RoleManagementPage from './pages/RoleManagementPage';
import MenuManagementPage from './pages/MenuManagementPage';

// Placeholder for the home page
const HomePage = () => <h2>Welcome Home!</h2>;

const ProtectedRoute = ({ children }: { children: React.ReactElement }) => {
    const isAuthenticated = useAuthStore((state: any) => state.isAuthenticated);
    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }
    return children;
};

const router = createBrowserRouter([
    {
        path: '/login',
        element: <LoginPage />,
    },
    {
        path: '/',
        element: (
            <ProtectedRoute>
                <MainLayout />
            </ProtectedRoute>
        ),
        children: [
            {
                index: true,
                element: <Navigate to="/users" replace />,
            },
            {
                path: 'users',
                element: <UserManagementPage />,
            },
            {
                path: 'roles',
                element: <RoleManagementPage />,
            },
            {
                path: 'menus',
                element: <MenuManagementPage />,
            }
            // Add other protected routes here as children
            // e.g. { path: 'dashboard', element: <DashboardPage /> }
        ],
    },
]);

const AppRouter = () => {
    return <RouterProvider router={router} />;
};

export default AppRouter; 