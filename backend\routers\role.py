from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from .. import schemas, services
from ..database import get_db

router = APIRouter(
    prefix="/api/roles",
    tags=["roles"],
)

@router.post("/", response_model=schemas.Role, status_code=status.HTTP_201_CREATED)
def create_role(role: schemas.RoleCreate, db: Session = Depends(get_db)):
    db_role = services.get_role_by_name(db, name=role.name)
    if db_role:
        raise HTTPException(status_code=400, detail="Role name already exists")
    return services.create_role(db=db, role=role)

@router.get("/", response_model=List[schemas.Role])
def read_roles(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    roles = services.get_roles(db, skip=skip, limit=limit)
    return roles

@router.get("/{role_id}", response_model=schemas.Role)
def read_role(role_id: int, db: Session = Depends(get_db)):
    db_role = services.get_role(db, role_id=role_id)
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role not found")
    return db_role

@router.put("/{role_id}", response_model=schemas.Role)
def update_role(role_id: int, role: schemas.RoleUpdate, db: Session = Depends(get_db)):
    db_role = services.update_role(db, role_id=role_id, role_update=role)
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role not found")
    return db_role

@router.delete("/{role_id}", response_model=schemas.Role)
def delete_role(role_id: int, db: Session = Depends(get_db)):
    db_role = services.delete_role(db, role_id=role_id)
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role not found")
    return db_role

@router.post("/{role_id}/permissions", response_model=schemas.Role)
def assign_permissions_to_role(role_id: int, permissions: schemas.RolePermissionAssignment, db: Session = Depends(get_db)):
    db_role = services.assign_permissions_to_role(db, role_id=role_id, permission_ids=permissions.permission_ids)
    if db_role is None:
        raise HTTPException(status_code=404, detail="Role not found")
    return db_role 