from pydantic import BaseModel
from typing import List, Optional
from .permission import Permission

class RoleBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    display_name: Optional[str] = None

class Role(RoleBase):
    id: int
    permissions: List[Permission] = []

    class Config:
        from_attributes = True

class RolePermissionAssignment(BaseModel):
    permission_ids: List[int] 